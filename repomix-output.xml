This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
aggregates/
  integration_event_aggregate.py
dto/
  integration_event_dto.py
entities/
  integration_event_entity.py
repositories/
  integration_event_repository.py
services/
  integration_event_domain_service.py
integration_event_factory.py
models.py
publish_replay_events.py
publisher.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="aggregates/integration_event_aggregate.py">
class IntegrationEventAggregate:
    def __init__(self, integration_event):
        self.integration_event = integration_event
</file>

<file path="dto/integration_event_dto.py">
from ths_common.constants.integration_event_constants import IntegrationEventType


class IntegrationEventDTO:
    def __init__(
        self,
        event_type: IntegrationEventType,
        hotel_id,
        generated_at,
        body,
        root_application=None,
    ):
        """
        :param event_type (IntegrationEventType): the type of event
        :param hotel_id: Hotel of the event
        :param generated_at: The time at which the event happend. Usually the 'modified_at' of the aggregate
        :param body: Dict
        :param root_application: str
        """
        self.body = body
        self.hotel_id = hotel_id
        self.event_type = event_type
        self.generated_at = generated_at
        self._user_action = None
        self.root_application = root_application

    @property
    def user_action(self):
        return self._user_action

    @user_action.setter
    def user_action(self, value):
        self._user_action = value
</file>

<file path="entities/integration_event_entity.py">
class IntegrationEventEntity:
    def __init__(
        self,
        event_type,
        hotel_id,
        generated_at,
        status,
        body,
        event_id,
        booking_id=None,
        user_action=None,
    ):
        self.body = body
        self.event_type = event_type
        self.generated_at = generated_at
        self.hotel_id = hotel_id
        self.status = status
        self.event_id = event_id
        self.booking_id = booking_id
        self.user_action = user_action
</file>

<file path="repositories/integration_event_repository.py">
import json

from object_registry import register_instance
from prometheus.domain.integration_event.aggregates.integration_event_aggregate import (
    IntegrationEventAggregate,
)
from prometheus.domain.integration_event.entities.integration_event_entity import (
    IntegrationEventEntity,
)
from prometheus.domain.integration_event.models import IntegrationEventModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.integration_event_constants import (
    IntegrationEventStatus,
    IntegrationEventType,
)
from ths_common.utils.common_utils import json_dumps


class IntegrationEventAdapter:
    @staticmethod
    def to_db_model(event_entity):
        integration_event = IntegrationEventModel()
        integration_event.event_id = event_entity.event_id
        integration_event.hotel_id = event_entity.hotel_id
        integration_event.generated_at = event_entity.generated_at
        # Dumping and loading to get a dict that can be converted into json
        # TODO this is ugly fix
        integration_event.body = json.loads(json_dumps(event_entity.body))
        integration_event.event_type = event_entity.event_type.value
        integration_event.status = event_entity.status.value
        integration_event.user_action = event_entity.user_action
        integration_event.booking_id = event_entity.booking_id
        return integration_event

    @staticmethod
    def to_entity(event_model):
        return IntegrationEventEntity(
            IntegrationEventType(event_model.event_type),
            event_model.hotel_id,
            event_model.generated_at,
            IntegrationEventStatus(event_model.status),
            event_model.body,
            event_model.event_id,
            booking_id=event_model.booking_id,
            user_action=event_model.user_action,
        )


@register_instance()
class IntegrationEventRepository(BaseRepository):
    def to_aggregate(self, integration_event_model, **kwargs):
        entity = IntegrationEventAdapter.to_entity(integration_event_model)
        return IntegrationEventAggregate(entity)

    def from_aggregate(self, aggregate=None):
        return IntegrationEventAdapter.to_db_model(aggregate.integration_event)

    def get_oldest_unpublished_event(self):
        integration_event_model = (
            self.session()
            .query(IntegrationEventModel)
            .filter(
                IntegrationEventModel.status.in_(
                    (
                        IntegrationEventStatus.UNPUBLISHED.value,
                        IntegrationEventStatus.FAILED.value,
                    )
                )
            )
            .order_by(IntegrationEventModel.generated_at.asc())
            .first()
        )
        if not integration_event_model:
            return None
        return self.to_aggregate(integration_event_model=integration_event_model)

    def filter_events(self, hotels_ids, event_types):
        integration_event_models = (
            self.session()
            .query(IntegrationEventModel)
            .filter(IntegrationEventModel.hotel_id.in_(hotels_ids))
            .filter(IntegrationEventModel.event_type.in_(event_types))
            .order_by(IntegrationEventModel.generated_at.asc())
            .all()
        )
        if not integration_event_models:
            return None
        return [
            self.to_aggregate(integration_event_model=integration_event_model)
            for integration_event_model in integration_event_models
        ]

    def save(self, event_aggregate):
        integration_event_model = self.from_aggregate(event_aggregate)
        self._save(integration_event_model)
        self.flush_session()

    def update(self, event_aggregate):
        integration_event_model = self.from_aggregate(event_aggregate)
        self._update(integration_event_model)
        self.flush_session()

    def count_unpublished_events(self):
        event_count = self.filter(
            IntegrationEventModel,
            IntegrationEventModel.status.in_(
                (
                    IntegrationEventStatus.UNPUBLISHED.value,
                    IntegrationEventStatus.FAILED.value,
                )
            ),
        ).count()
        return event_count

    def get_all_unpublished_events(self):
        events = (
            self.filter(
                IntegrationEventModel,
                IntegrationEventModel.status.in_(
                    (
                        IntegrationEventStatus.UNPUBLISHED.value,
                        IntegrationEventStatus.FAILED.value,
                    )
                ),
            )
            .order_by(IntegrationEventModel.generated_at.asc())
            .all()
        )

        return [self.to_aggregate(integration_event_model=event) for event in events]
</file>

<file path="services/integration_event_domain_service.py">
import logging

from ths_common.constants.integration_event_constants import IntegrationEventStatus

logger = logging.getLogger(__name__)


class IntegrationEventDomainService:
    def __init__(self, integration_event_publisher):
        self.integration_event_publisher = integration_event_publisher

    def publish_to_queue(self, event_aggregate):
        event_entity = event_aggregate.integration_event

        try:
            self.integration_event_publisher.publish(event_entity)
            event_entity.status = IntegrationEventStatus.PUBLISHED
            return event_aggregate
        except Exception as e:
            logger.exception(
                'Error while publishing integration event with id: %s',
                event_entity.event_id,
            )
            event_entity.status = IntegrationEventStatus.FAILED
            return event_aggregate
</file>

<file path="integration_event_factory.py">
from prometheus.core.globals import crs_context
from prometheus.domain.integration_event.aggregates.integration_event_aggregate import (
    IntegrationEventAggregate,
)
from prometheus.domain.integration_event.entities.integration_event_entity import (
    IntegrationEventEntity,
)
from ths_common.constants.integration_event_constants import IntegrationEventStatus
from ths_common.utils.id_generator_utils import random_id_generator


class IntegrationEventFactory:
    @staticmethod
    def create_integration_event(integration_event_dto):
        event_type = integration_event_dto.event_type
        hotel_id = integration_event_dto.hotel_id
        generated_at = integration_event_dto.generated_at
        event_id = random_id_generator('EVT')
        current_booking_id = crs_context.get_current_booking_id()

        body = dict(
            message_id=event_id,
            generated_at=generated_at,
            events=integration_event_dto.body,
            user_action=integration_event_dto.user_action,
            event_type=event_type.value,
            root_application=integration_event_dto.root_application,
        )
        event_entity = IntegrationEventEntity(
            event_type=event_type,
            hotel_id=hotel_id,
            generated_at=generated_at,
            status=IntegrationEventStatus.UNPUBLISHED,
            body=body,
            event_id=event_id,
            user_action=integration_event_dto.user_action,
            booking_id=current_booking_id,
        )
        return IntegrationEventAggregate(event_entity)
</file>

<file path="models.py">
from sqlalchemy import JSON, Column, DateTime, Index, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin


class IntegrationEventModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "integration_event"

    event_id = Column('event_id', String, primary_key=True)
    hotel_id = Column('hotel_id', String, nullable=True, index=True)
    event_type = Column('event_type', String, nullable=False, index=True)
    generated_at = Column('generated_at', DateTime(timezone=True), nullable=False)
    body = Column('body', JSON, nullable=False)
    status = Column(
        'status', String, nullable=False
    )  # populated from IntegrationEventStatus
    user_action = Column(String)
    booking_id = Column(String)
    # bill id
    # seller id

    __table_args__ = (
        Index(
            'idx_status_not_published', status, postgresql_where=(status != "published")
        ),
        Index('ordering_generated_at_asc', generated_at.asc()),
    )
</file>

<file path="publish_replay_events.py">
import logging

from kombu import Exchange, Producer
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.exceptions import CRSException

logger = logging.getLogger(__name__)


@register_instance()
class ReplayIntegrationEventPublisher(BaseQueueService):
    def _setup_entities(self):
        self._integration_event_exchange = Exchange(
            'crs-replay-events', type='topic', durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )

    def publish(self, event):
        current_tenant_id = get_current_tenant_id()
        self._initialize()
        payload = ReplayIntegrationEventPublisher.get_event_payload(event)

        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[current_tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    current_tenant_id
                )
            )

        self._publish(
            self._tenant_wise_producers[current_tenant_id],
            payload,
            event.event_type.routing_key,
        )

    @staticmethod
    def get_event_payload(event):
        return event.body
</file>

<file path="publisher.py">
import logging

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.exceptions import CRSException

logger = logging.getLogger(__name__)


@register_instance()
class IntegrationEventPublisher(BaseQueueService):
    def _setup_entities(self):
        argument = {'hash-header': 'hash-on'}
        self._integration_event_exchange = Exchange(
            'crs-events',
            type='topic',
            durable=True,
            argument=argument,
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )

    def publish(self, event):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()
        payload = IntegrationEventPublisher.get_event_payload(event)
        headers = IntegrationEventPublisher.get_event_headers(event)

        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[current_tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    current_tenant_id
                )
            )

        self._publish(
            self._tenant_wise_producers[current_tenant_id],
            payload,
            event.event_type.routing_key,
            headers=headers,
        )

    @staticmethod
    def get_event_payload(event):
        return event.body

    @staticmethod
    def get_event_headers(event):
        hotel_id = getattr(event, 'hotel_id', None)
        if hotel_id:
            return {'hash-on': hotel_id}
</file>

</files>
