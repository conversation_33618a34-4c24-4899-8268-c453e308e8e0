const Config = require('../tenants')
const {TENANT_SERVICE_URL, CATALOG_SERVICE_URL} = require('./urls')


module.exports.sns = () => {
    return 'arn:aws:sns:ap-southeast-1:605536185498:superhero-nightaudits'
};

module.exports.intouchTenantFilter = () => {
    return Config.getEnabledTenants('reports.is_intouch_enabled', TENANT_SERVICE_URL, CATALOG_SERVICE_URL)
};

module.exports.acdcTenantFilter = () => {
    return Config.getEnabledTenants('reports.is_acdc_enabled', TENANT_SERVICE_URL, CATALOG_SERVICE_URL)
};

module.exports.acqTenantFilter = () => {
    return Config.getEnabledTenants('acq.is_enabled', TENANT_SERVICE_URL, CATALOG_SERVICE_URL)
};
