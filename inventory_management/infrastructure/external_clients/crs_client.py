from object_registry import register_instance
from shared_kernel.infrastructure.external_clients.crs_client import CR<PERSON>lient
from ths_common.constants.booking_constants import BookingStatus
from thsc.crs.entities.booking import Booking, BookingSearchQuery, Price, Room


@register_instance()
class CRSClient(CRSClient):
    @staticmethod
    def setup_context():
        from thsc.crs import context

        context.application = "inventory_management_service"

    @classmethod
    def get_bookings_for_checkin_range(
        cls, checkin_start, checkin_end, crs_room_type_id
    ):
        search_query = BookingSearchQuery(
            checkin_start=checkin_start,
            checkin_end=checkin_end,
            status=[BookingStatus.RESERVED, BookingStatus.CONFIRMED],
            room_type_ids=crs_room_type_id,
        )
        search_result = Booking.get_booking_configs(search_query)
        return cls._fetch_pending_bookings(search_result, search_query)

    @classmethod
    def _fetch_pending_bookings(cls, search_result, search_query):
        bookings = search_result.bookings
        while search_result.total > len(bookings):
            search_query.offset = len(bookings)
            res = Booking.get_booking_configs(search_query)
            bookings.extend(res.bookings)
        return bookings

    @classmethod
    def upgrade_booking(
        cls,
        booking: Booking,
        room_type,
        room_amount_increment=0,
        price_change_required=False,
    ):
        cls.setup_context()
        for room in booking.rooms:
            room_instance_for_update = Room.create_instance_for_update(
                room_stay_id=room.room_stay_id
            )
            room_instance_for_update.room_type_id = room_type
            prices = []
            if price_change_required:
                for price in room.prices:
                    price = Price.create_instance_for_update(
                        charge_id=price.charge_id,
                        posttax_amount=price.posttax_amount + room_amount_increment,
                    )
                    prices.append(price)
                room_instance_for_update.prices = prices
            booking.update_room(
                room_instance_for_update, price_change_required=price_change_required
            )

    @staticmethod
    def get_booking(booking_id):
        return Booking.get(booking_id)
