from sqlalchemy import func
from treebo_commons.utils import dateutils

from inventory_management.extensions import db


class TimeStampMixin(object):
    """
    model for created time and modified time
    """

    created_at = db.Column(
        db.DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    modified_at = db.Column(
        db.DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )


class DeleteMixin(object):
    deleted = db.Column('deleted', db.Boolean, default=False)


class OptinStatusModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "optin_status"

    hotel_id = db.Column(db.String, primary_key=True)
    optin_status_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lower_room_type_id = db.Column(
        db.Integer, db.<PERSON><PERSON><PERSON>("room_type.room_type_id"), nullable=False
    )
    higher_room_type_id = db.Column(
        db.Integer, db.<PERSON><PERSON><PERSON>("room_type.room_type_id"), nullable=False
    )
    upgrade_type = db.Column(db.String, nullable=False)
    optin_status = db.Column(db.Boolean, nullable=False)
    lower_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[lower_room_type_id]
    )
    higher_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[higher_room_type_id]
    )


class CustomPriceSlabModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "custom_price_slab"

    hotel_id = db.Column(db.String, primary_key=True)
    slab_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lower_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    higher_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    slab_type = db.Column(db.String, nullable=False)
    slab_value = db.Column(db.Integer, nullable=False)
    lower_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[lower_room_type_id]
    )
    higher_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[higher_room_type_id]
    )


class DefaultPriceSlabModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "default_price_slab"

    slab_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lower_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    higher_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    slab_type = db.Column(db.String, nullable=False)
    slab_value = db.Column(db.Integer, nullable=False)
    lower_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[lower_room_type_id]
    )
    higher_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[higher_room_type_id]
    )


class AvailableRoomTypeUpgradeModel(db.Model, TimeStampMixin):
    __tablename__ = "available_room_type_upgrade"

    room_type_upgrade_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lower_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    higher_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    upgrade_priority = db.Column(db.Integer, nullable=False, unique=True)


class TimeConfigModel(db.Model, TimeStampMixin):
    __tablename__ = "time_config"

    config_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    abw_start = db.Column(db.Integer, nullable=False)
    abw_end = db.Column(db.Integer, nullable=False)
    time_range_start = db.Column(db.Time, nullable=False)
    time_range_end = db.Column(db.Time, nullable=False)
    cron_interval_hours = db.Column(db.Integer, nullable=False)
    last_run = db.Column(db.DateTime, nullable=False, default=dateutils.current_time)


class OccupancyConfigModel(db.Model, TimeStampMixin):
    __tablename__ = "occupancy_config"

    config_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    higher_room_max_occupancy = db.Column(db.Integer, nullable=False)
    higher_room_min_occupancy = db.Column(db.Integer, nullable=False)
    lower_room_max_occupancy = db.Column(db.Integer, nullable=False)
    lower_room_min_occupancy = db.Column(db.Integer, nullable=False)


class RoomUpgradeFormulaModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "room_upgrade_formula"

    formula_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    time_config_id = db.Column(
        db.Integer, db.ForeignKey("time_config.config_id"), nullable=False
    )
    occupancy_config_id = db.Column(
        db.Integer, db.ForeignKey("occupancy_config.config_id"), nullable=False
    )
    lower_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=True, default=None
    )
    higher_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=True, default=None
    )
    higher_room_type_fill_percent = db.Column(db.Integer, nullable=False)
    upgrade_type = db.Column(db.String, nullable=False)
    slab_type = db.Column(db.String, nullable=True)


class ProcessedRoomUpgradeModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "processed_room_upgrade"

    booking_id = db.Column(db.String, primary_key=True)
    room_stay_id = db.Column(db.String, primary_key=True)
    hotel_id = db.Column(db.String, primary_key=True)
    lower_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    higher_room_type_id = db.Column(
        db.Integer, db.ForeignKey("room_type.room_type_id"), nullable=False
    )
    formula_id = db.Column(
        db.Integer, db.ForeignKey("room_upgrade_formula.formula_id"), nullable=False
    )
    upgrade_type = db.Column(db.String, nullable=False)
    upgrade_amount = db.Column(db.DECIMAL, nullable=False)
    upgrade_status = db.Column(db.String, nullable=False)
    lower_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[lower_room_type_id]
    )
    higher_room_type = db.relationship(
        "RoomTypeModel", foreign_keys=[higher_room_type_id]
    )


class RoomTypeModel(db.Model, DeleteMixin, TimeStampMixin):
    __tablename__ = "room_type"

    room_type_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String, nullable=False, unique=True)
    crs_id = db.Column(db.String, nullable=False, unique=True)
    level = db.Column(db.Integer, nullable=False, unique=True)

    def __str__(self):
        return self.name
