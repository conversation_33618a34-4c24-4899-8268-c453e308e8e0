from inventory_management.domain.constants import SlabType
from inventory_management.domain.entities.paid_upgrade_price_slab import (
    PaidUpgradePriceSlab,
)
from inventory_management.infrastructure.database.repositories.models import (
    CustomPriceSlabModel,
)


class PriceSlabAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: PaidUpgradePriceSlab, **kwargs):
        # noinspection PyArgumentList
        CustomPriceSlabModel(
            hotel_id=domain_entity.hotel_id,
            lower_room_type=domain_entity.room_type_upgrade.lower_room_type.id,
            higher_room_type=domain_entity.room_type_upgrade.higher_room_type.id,
            slab_type=domain_entity.slab_type.value,
            slab_value=domain_entity.slab_value,
        )
        pass

    @staticmethod
    def to_domain_entity(db_entity, **kwargs):
        return PaidUpgradePriceSlab(
            room_type_upgrade=kwargs.get('room_type_upgrade'),
            slab_type=SlabType(db_entity.slab_type),
            slab_value=db_entity.slab_velue,
            hotel_id=getattr(db_entity, "hotel_id"),
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
