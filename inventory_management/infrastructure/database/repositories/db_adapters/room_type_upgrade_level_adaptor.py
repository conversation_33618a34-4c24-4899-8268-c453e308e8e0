from inventory_management.domain.entities.room_type_upgrade_level import (
    RoomTypeUpgradeLevel,
)
from inventory_management.infrastructure.database.repositories.models import (
    AvailableRoomTypeUpgradeModel,
)


class RoomTypeUpgradeLevelAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: RoomTypeUpgradeLevel, **kwargs):
        # noinspection PyArgumentList
        pass

    @staticmethod
    def to_domain_entity(db_entity: AvailableRoomTypeUpgradeModel, **kwargs):
        return RoomTypeUpgradeLevel(
            upgrade_priority=db_entity.upgrade_priority,
            room_type_upgrade=kwargs.get('room_type_upgrade'),
        )
