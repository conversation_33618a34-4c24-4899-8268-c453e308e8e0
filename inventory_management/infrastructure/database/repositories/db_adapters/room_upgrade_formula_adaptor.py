from inventory_management.domain.constants import SlabType, UpgradeType
from inventory_management.domain.entities.room_upgrade_formula import RoomUpgradeFormula
from inventory_management.domain.value_objects.bookings_to_pick import BookingsToPick
from inventory_management.domain.value_objects.occupancy_pre_condition import (
    OccupancyPreConditions,
)
from inventory_management.domain.value_objects.time_to_run import TimeToRun


class RoomUpgradeFormulaAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: RoomUpgradeFormula, **kwargs):
        # noinspection PyArgumentList
        pass

    @staticmethod
    def to_domain_entity(db_entity, **kwargs):
        time_config_db_entity = db_entity.TimeConfigModel
        occupancy_config_db_entity = db_entity.OccupancyConfigModel
        bookings_to_pick = BookingsToPick(
            abw_start=time_config_db_entity.abw_start,
            abw_end=time_config_db_entity.abw_end,
        )
        time_to_run = TimeToRun(
            time_range_start=time_config_db_entity.time_range_start,
            time_range_end=time_config_db_entity.time_range_end,
            cron_interval_hours=time_config_db_entity.cron_interval_hours,
        )
        occupancy_pre_condition = OccupancyPreConditions(
            lower_room_max_occupancy=occupancy_config_db_entity.lower_room_max_occupancy,
            lower_room_min_occupancy=occupancy_config_db_entity.lower_room_min_occupancy,
            higher_room_max_occupancy=occupancy_config_db_entity.higher_room_max_occupancy,
            higher_room_min_occupancy=occupancy_config_db_entity.higher_room_min_occupancy,
        )
        return RoomUpgradeFormula(
            formula_id=db_entity.RoomUpgradeFormulaModel.formula_id,
            upgrade_type=UpgradeType(db_entity.RoomUpgradeFormulaModel.upgrade_type),
            slab_type=SlabType(db_entity.RoomUpgradeFormulaModel.slab_type),
            higher_room_type_fill_percent=db_entity.RoomUpgradeFormulaModel.higher_room_type_fill_percent,
            bookings_to_pick=bookings_to_pick,
            time_to_run=time_to_run,
            occupancy_pre_condition=occupancy_pre_condition,
            room_type_upgrade=kwargs.get('room_type_upgrade'),
        )
