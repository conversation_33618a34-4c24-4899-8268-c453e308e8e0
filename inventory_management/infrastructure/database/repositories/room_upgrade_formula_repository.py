from treebo_commons.utils import dateutils

from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.base_repository import BaseRepository
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_adaptor import (
    RoomTypeAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_type_upgrade_level_adaptor import (
    RoomTypeUpgradeLevelAdaptor,
)
from inventory_management.infrastructure.database.repositories.db_adapters.room_upgrade_formula_adaptor import (
    RoomUpgradeFormulaAdaptor,
)
from inventory_management.infrastructure.database.repositories.models import (
    OccupancyConfigModel,
    RoomTypeModel,
    RoomUpgradeFormulaModel,
    TimeConfigModel,
)
from object_registry import register_instance


@register_instance()
class RoomUpgradeFormulaRepository(BaseRepository):
    room_upgrade_formula_adaptor = RoomUpgradeFormulaAdaptor()
    room_type_adaptor = RoomTypeAdaptor()
    room_type_upgrade_level = RoomTypeUpgradeLevelAdaptor()

    def to_aggregate(self, **kwargs):
        room_upgrade_formula = kwargs['room_upgrade_formula']
        higher_room_type_id = kwargs['higher_room_type_id']
        lower_room_type_id = kwargs['lower_room_type_id']
        higher_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == higher_room_type_id)
            .one()
        )
        lower_room_type = self.room_type_adaptor.to_domain_entity(
            self.query(RoomTypeModel)
            .filter(RoomTypeModel.room_type_id == lower_room_type_id)
            .one()
        )
        room_type_upgrade = RoomTypeUpgrade(
            lower_room_type=lower_room_type, higher_room_type=higher_room_type
        )
        return self.room_upgrade_formula_adaptor.to_domain_entity(
            room_upgrade_formula, room_type_upgrade=room_type_upgrade
        )

    def from_aggregate(self, aggregate=None):
        pass

    def load(self, lower_room_type_id, higher_room_type_id):
        current_time = dateutils.current_time()
        room_upgrade_formula_entries = (
            self.query(RoomUpgradeFormulaModel, OccupancyConfigModel, TimeConfigModel)
            .join(TimeConfigModel, OccupancyConfigModel)
            .filter(TimeConfigModel.time_range_start < current_time)
            .filter(TimeConfigModel.time_range_end >= current_time)
            .all()
        )
        for entry in room_upgrade_formula_entries[:]:
            if not entry.TimeConfigModel.last_run.replace(
                tzinfo=dateutils.local_timezone()
            ) < dateutils.subtract(
                dateutils.current_datetime(),
                minutes=entry.TimeConfigModel.cron_interval_hours * 60,
            ):
                room_upgrade_formula_entries.remove(entry)
                continue
            if (
                entry.RoomUpgradeFormulaModel.lower_room_type_id
                and entry.RoomUpgradeFormulaModel.lower_room_type_id
                != lower_room_type_id
            ):
                room_upgrade_formula_entries.remove(entry)
                continue
            if (
                entry.RoomUpgradeFormulaModel.higher_room_type_id
                and entry.RoomUpgradeFormulaModel.higher_room_type_id
                != higher_room_type_id
            ):
                room_upgrade_formula_entries.remove(entry)
                continue

        final_entries = []
        for entry in room_upgrade_formula_entries:
            if (
                entry.RoomUpgradeFormulaModel.lower_room_type_id == lower_room_type_id
                and entry.RoomUpgradeFormulaModel.higher_room_type_id
                == higher_room_type_id
            ):
                final_entries.append(entry)

        if not final_entries:
            final_entries = room_upgrade_formula_entries

        return [
            self.to_aggregate(
                room_upgrade_formula=room_upgrade_formula,
                higher_room_type_id=higher_room_type_id,
                lower_room_type_id=lower_room_type_id,
            )
            for room_upgrade_formula in final_entries
        ]
