from sqlalchemy import inspect
from treebo_commons.utils import dateutils

from inventory_management.admin_views.base_view import RoleBasedAccessControlView
from inventory_management.domain.constants import SlabType, UpgradeType
from inventory_management.infrastructure.database.repositories.models import (
    CustomPriceSlabModel,
    DefaultPriceSlabModel,
    OptinStatusModel,
    ProcessedRoomUpgradeModel,
)


class OptinStatusAdminView(RoleBasedAccessControlView):
    _model = OptinStatusModel
    _name = 'Optout Hotel Listing'
    column_sortable_list = ()
    can_edit = False
    column_display_pk = True
    column_editable_list = ['optin_status']
    column_searchable_list = ['hotel_id']
    column_list = ('hotel_id', 'lower_room_type', 'higher_room_type', 'upgrade_type')
    form_columns = [
        'hotel_id',
        'lower_room_type',
        'higher_room_type',
        'upgrade_type',
        'optin_status',
    ]
    form_choices = {
        "upgrade_type": [
            (UpgradeType.FREE.value, UpgradeType.FREE.value),
            (UpgradeType.PAID.value, UpgradeType.PAID.value),
        ]
    }


class DefaultPriceSlabAdminView(RoleBasedAccessControlView):
    _model = DefaultPriceSlabModel
    _category = 'Price Slabs'
    _name = 'Default Slabs'
    column_sortable_list = ()
    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    column_editable_list = ['slab_value']
    column_list = ('lower_room_type', 'higher_room_type', 'slab_type', 'slab_value')
    form_excluded_columns = ['created_at', 'modified_at', 'slab_id']
    column_default_sort = 'created_at'


class CustomPriceSlabAdminView(RoleBasedAccessControlView):
    _model = CustomPriceSlabModel
    _category = 'Price Slabs'
    _name = 'Custom Slabs'
    column_sortable_list = ()
    can_edit = False
    column_display_pk = True
    column_editable_list = ['slab_value', 'slab_type']
    column_list = (
        'hotel_id',
        'lower_room_type',
        'higher_room_type',
        'slab_type',
        'slab_value',
    )
    form_columns = [
        'hotel_id',
        'lower_room_type',
        'higher_room_type',
        'slab_type',
        'slab_value',
    ]
    form_excluded_columns = ['created_at', 'modified_at', 'slab_id']
    column_choices = {
        'slab_type': [
            (SlabType.LOWER.value, SlabType.LOWER.value),
            (SlabType.HIGHER.value, SlabType.HIGHER.value),
        ]
    }
    form_choices = {
        "slab_type": [
            (SlabType.LOWER.value, SlabType.LOWER.value),
            (SlabType.HIGHER.value, SlabType.HIGHER.value),
        ]
    }


class ProcessedRoomUpgradeAdminView(RoleBasedAccessControlView):
    _model = ProcessedRoomUpgradeModel
    _name = 'Processed Upgrades'
    column_sortable_list = ()
    can_edit = False
    can_delete = False
    can_create = False
    can_export = False
    column_display_pk = True
    column_labels = dict(
        created_at='Initiated At',
        modified_at='Completed At',
        lower_room_type='Old Room',
        higher_room_type='New Room',
        upgrade_amount='Amount',
        upgrade_status='Status',
        booking_id='Booking',
        room_stay_id='Room',
    )
    column_list = (
        'booking_id',
        'room_stay_id',
        'hotel_id',
        'lower_room_type',
        'higher_room_type',
        'upgrade_amount',
        'upgrade_status',
        'created_at',
        'modified_at',
    )

    def _date_time_formatter(self, context, model, name):
        return dateutils.datetime_to_str(getattr(model, name))

    column_formatters = {
        'created_at': _date_time_formatter,
        'modified_at': _date_time_formatter,
    }
