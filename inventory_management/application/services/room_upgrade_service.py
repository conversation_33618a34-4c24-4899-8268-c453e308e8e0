import logging
from typing import List

from inventory_management.application.dtos.room_type_inventory_dto import (
    RoomTypeInventories,
)
from inventory_management.application.services.communication_service import (
    CommunicationService,
)
from inventory_management.common.decorators import handle_db_commits
from inventory_management.domain.constants import (
    UPGRADE_PRIORITY,
    BookingTypes,
    UpgradeStatus,
    UpgradeType,
)
from inventory_management.domain.entities.processed_room_upgrade import (
    ProcessedRoomUpgrade,
)
from inventory_management.domain.entities.room_upgrade_formula import RoomUpgradeFormula
from inventory_management.domain.value_objects.formula_bookings_mappings import (
    FormulaBookingsMapping,
)
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.infrastructure.database.repositories.optin_status_repository import (
    OptinStatusRepository,
)
from inventory_management.infrastructure.database.repositories.paid_upgrade_price_slab_repository import (
    PaidUpgradePriceSlabRepository,
)
from inventory_management.infrastructure.database.repositories.processed_room_upgrade_repository import (
    ProcessedRoomUpgradeRepository,
)
from inventory_management.infrastructure.database.repositories.room_type_repository import (
    RoomTypeRepository,
)
from inventory_management.infrastructure.database.repositories.room_type_upgrade_repository import (
    RoomTypeUpgradeLevelRepository,
)
from inventory_management.infrastructure.database.repositories.room_upgrade_formula_repository import (
    RoomUpgradeFormulaRepository,
)
from inventory_management.infrastructure.external_clients.crs_client import CRSClient
from object_registry import register_instance
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.utils.common_utils import group_list
from thsc.crs.entities.booking import Booking

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        RoomUpgradeFormulaRepository,
        PaidUpgradePriceSlabRepository,
        ProcessedRoomUpgradeRepository,
        RoomTypeRepository,
        OptinStatusRepository,
        RoomTypeUpgradeLevelRepository,
        CRSClient,
        CatalogServiceClient,
        CommunicationService,
    ]
)
class RoomUpgradeService:
    def __init__(
        self,
        room_upgrade_formula_repository,
        price_slab_repository,
        processed_room_upgrade_repository,
        room_type_repository,
        optin_status_repository,
        room_type_upgrade_level_repository,
        crs_client,
        catalog_service_client,
        communication_service,
    ):
        self.room_upgrade_formula_repository = room_upgrade_formula_repository
        self.price_slab_repository = price_slab_repository
        self.processed_room_upgrade_repository = processed_room_upgrade_repository
        self.room_type_repository = room_type_repository
        self.optin_status_repository = optin_status_repository
        self.room_type_upgrade_level_repository = room_type_upgrade_level_repository
        self.crs_client = crs_client
        self.catalog_service_client = catalog_service_client
        self.communication_service = communication_service

    def process_config(self, hotel_ids=None):
        logger.info("Running booking upgrade")
        room_type_upgrade_levels = self.room_type_upgrade_level_repository.load_all()
        for room_type_upgrade_level in room_type_upgrade_levels:
            room_type_upgrade = room_type_upgrade_level.room_type_upgrade
            logger.info(
                "Processing room upgrades from room type: %s to %s",
                room_type_upgrade.lower_room_type.name,
                room_type_upgrade.higher_room_type.name,
            )
            formulas = self.room_upgrade_formula_repository.load(
                lower_room_type_id=room_type_upgrade.lower_room_type.id,
                higher_room_type_id=room_type_upgrade.higher_room_type.id,
            )
            free_upgrade_formulas, paid_upgrade_formulas = self._segregate_formulas(
                formulas
            )
            if not free_upgrade_formulas:
                continue

            self._apply_free_upgrade(free_upgrade_formulas, room_type_upgrade)

    @staticmethod
    def _segregate_formulas(formulas: List[RoomUpgradeFormula]):
        free_upgrade_formulas = [
            formula for formula in formulas if formula.upgrade_type == UpgradeType.FREE
        ]
        paid_upgrade_formulas = [
            formula for formula in formulas if formula.upgrade_type == UpgradeType.PAID
        ]
        return free_upgrade_formulas, paid_upgrade_formulas

    def _apply_free_upgrade(self, free_upgrade_formulas, room_type_upgrade):
        logger.info(
            "Processing room upgrades for free upgrade formulas: %s",
            [f.formula_id for f in free_upgrade_formulas],
        )
        bookings = self._get_bookings_eligible_for_upgrade(free_upgrade_formulas)

        for hotel_id, bookings in group_list(bookings, 'hotel_id').items():
            if self._is_opted_out(hotel_id, room_type_upgrade):
                logging.info("Hotel: %s is opted out from free upgrade", hotel_id)
                continue

            formula_booking_mappings = (
                self._prepare_formula_booking_mappings_with_inventory(
                    hotel_id, room_type_upgrade, bookings, free_upgrade_formulas
                )
            )
            if not formula_booking_mappings:
                logger.info("No formula booking mappings found")
                continue

            for mapping in formula_booking_mappings:
                for booking in mapping.bookings:
                    is_upgraded = self._upgrade_booking_for_formula(
                        booking=booking, mapping=mapping
                    )
                    if not is_upgraded:
                        logger.info(
                            "Booking: %s could not be upgraded", booking.booking_id
                        )
                        continue
                    logger.info("Booking: %s upgraded successfully")
                    self._trigger_communication(booking, mapping.formula)

    def _get_bookings_eligible_for_upgrade(self, formulas: List[RoomUpgradeFormula]):
        distinct_checkin_checkout_pairs = {
            (f.checkin_start_date, f.checkin_end_date) for f in formulas
        }
        crs_room_type_id = formulas[0].room_type_upgrade.lower_room_type.crs_id

        bookings = []
        for distinct_checkin_checkout_pair in distinct_checkin_checkout_pairs:
            checkin_start_date, checkin_end_date = distinct_checkin_checkout_pair
            bookings.extend(
                self.crs_client.get_bookings_for_checkin_range(
                    checkin_start=checkin_start_date,
                    checkin_end=checkin_end_date,
                    crs_room_type_id=crs_room_type_id,
                )
            )

        return sorted(
            [b for b in bookings if self.is_booking_eligible_for_upgrade(b)],
            key=self._sort_logic,
        )

    def is_booking_eligible_for_upgrade(self, booking):
        # Don't process bookings with different room types
        if len({room.room_type_id for room in booking.rooms}) > 1:
            return False

        # Don't process bookings having room stays with different stay dates
        if len({(room.checkin_date, room.checkout_date) for room in booking.rooms}) > 1:
            return False

        # Already upgraded check - Booking should not have been upgraded before
        if self.processed_room_upgrade_repository.load_processed_room_upgrades(
            booking_id=booking.booking_id
        ):
            return False

        return True

    def _is_opted_out(self, hotel_id, room_type_upgrade: RoomTypeUpgrade):
        optin_status = self.optin_status_repository.load(
            hotel_id=hotel_id,
            lower_room_type_id=room_type_upgrade.lower_room_type.id,
            higher_room_type_id=room_type_upgrade.higher_room_type.id,
        )
        return not optin_status.optin_status if optin_status else False

    def _prepare_formula_booking_mappings_with_inventory(
        self,
        hotel_id,
        room_type_upgrade,
        bookings: List[Booking],
        formulas: List[RoomUpgradeFormula],
    ):
        (
            lower_rt_total_inventory,
            higher_rt_total_inventory,
        ) = self._get_total_inventory_for_room_type_upgrade(
            hotel_id=hotel_id, room_type_upgrade=room_type_upgrade
        )
        if higher_rt_total_inventory == 0:
            logger.info(
                "Higher room type total inventory is 0 for hotel: %s and room type: %s",
                hotel_id,
                room_type_upgrade.higher_room_type.name,
            )
            return

        formula_id_to_mapping = {
            formula.formula_id: FormulaBookingsMapping(formula) for formula in formulas
        }
        for booking in bookings:
            logger.debug(
                "Checking booking: %s for compliance with any formula", booking
            )
            for formula in formulas:
                booking_stored = self._store_booking_if_compliant_with_formula(
                    booking,
                    formula,
                    higher_rt_total_inventory,
                    lower_rt_total_inventory,
                    formula_id_to_mapping[formula.formula_id],
                )
                if booking_stored:
                    logger.info(
                        "Booking: %s mapped to formula: %s",
                        booking.booking_id,
                        formula.formula_id,
                    )
                    break

        return [
            mapping for mapping in formula_id_to_mapping.values() if mapping.bookings
        ]

    def _get_total_inventory_for_room_type_upgrade(
        self, hotel_id, room_type_upgrade: RoomTypeUpgrade
    ):
        lower_room_type_crs_id = room_type_upgrade.lower_room_type.crs_id
        higher_room_type_crs_id = room_type_upgrade.higher_room_type.crs_id
        all_room_types_inventory_data = (
            self.catalog_service_client.get_room_type_config(hotel_id)
        )

        lower_room_type_total_inventory = next(
            (
                room_type['room_count']
                for room_type in all_room_types_inventory_data
                if room_type['room_type']['code'] == lower_room_type_crs_id
            ),
            0,
        )

        higher_room_type_total_inventory = next(
            (
                room_type['room_count']
                for room_type in all_room_types_inventory_data
                if room_type['room_type']['code'] == higher_room_type_crs_id
            ),
            0,
        )

        return lower_room_type_total_inventory, higher_room_type_total_inventory

    def _store_booking_if_compliant_with_formula(
        self,
        booking: Booking,
        formula: RoomUpgradeFormula,
        higher_rt_total_inventory,
        lower_rt_total_inventory,
        formula_booking_mappings,
    ):
        if not formula.is_applicable_to_booking(booking):
            logger.debug(
                "Booking: %s is not applicable to formula: %s",
                booking.booking_id,
                formula.formula_id,
            )
            return False

        day_wise_higher_rt_availability = self._get_room_type_inventories(
            booking, formula.higher_room_type.crs_id
        )
        higher_rt_occupancy_percent = (
            day_wise_higher_rt_availability.get_maximum_occupancy_percent(
                higher_rt_total_inventory
            )
        )
        if higher_rt_occupancy_percent >= 100:
            logger.debug(
                "Higher room type is completely filled for hotel: %s and room type: %s",
                booking.hotel_id,
                formula.higher_room_type.name,
            )
            return False

        day_wise_lower_rt_availability = self._get_room_type_inventories(
            booking, formula.lower_room_type.crs_id
        )
        lower_rt_occupancy_percent = (
            day_wise_lower_rt_availability.get_maximum_occupancy_percent(
                lower_rt_total_inventory
            )
        )
        if not formula.satisfies_occupancy_pre_condition(
            lower_rt_occupancy_percent, higher_rt_occupancy_percent
        ):
            logger.debug(
                "Booking: %s does not satisfy pre condition for formula: %s. higher_rt_occupancy_percent: %s,"
                " lower_rt_occupancy_percent: %s",
                booking.booking_id,
                formula.formula_id,
                higher_rt_occupancy_percent,
                lower_rt_occupancy_percent,
            )
            return False

        formula_booking_mappings.bookings.append(booking)
        formula_booking_mappings.accumulate_inventory_available_for_upgrade(
            day_wise_higher_rt_availability
        )
        return True

    def _get_room_type_inventories(
        self, booking, crs_room_type_id
    ) -> RoomTypeInventories:
        return RoomTypeInventories(
            self.crs_client.get_room_type_inventories(
                hotel_id=booking.hotel_id,
                checkin=booking.checkin_date,
                checkout=booking.checkout_date,
                crs_room_type_id=crs_room_type_id,
            )
        )

    @handle_db_commits
    def _upgrade_booking_for_formula(
        self, booking: Booking, mapping: FormulaBookingsMapping
    ):
        logger.info(
            "Upgrading booking: %s to %s room type",
            booking.booking_id,
            mapping.formula.higher_room_type.name,
        )
        if not mapping.is_inventory_available_for_upgrade(booking):
            logger.info(
                "Sufficient inventory is not available for booking: %s to be upgrades",
                booking.booking_id,
            )
            return False

        self.crs_client.upgrade_booking(
            booking=booking, room_type=mapping.formula.higher_room_type.crs_id
        )
        mapping.decrease_inventory_available_for_upgrade(booking=booking)

        for room in booking.rooms:
            processed_room_upgrade = ProcessedRoomUpgrade(
                formula_id=mapping.formula.formula_id,
                booking_id=booking.booking_id,
                room_stay_id=room.room_stay_id,
                hotel_id=booking.hotel_id,
                room_type_upgrade=mapping.formula.room_type_upgrade,
                upgrade_type=mapping.formula.upgrade_type,
                upgrade_status=UpgradeStatus.COMPLETED,
            )
            self.processed_room_upgrade_repository.save(processed_room_upgrade)
        return True

    @staticmethod
    def _sort_logic(booking, sort_order=UPGRADE_PRIORITY):
        if booking.source.channel_code == BookingChannels.B2B.value:
            return sort_order[BookingTypes.CORPORATE]
        if booking.source.channel_code == BookingChannels.DIRECT.value:
            return sort_order[BookingTypes.DIRECT]
        if (
            booking.source.channel_code == BookingChannels.OTA.value
            and booking.status == BookingStatus.CONFIRMED
        ):
            return sort_order[BookingTypes.OTA_PREPAID]
        return sort_order[BookingTypes.UNCLASSIFIED]

    def _trigger_communication(self, booking, formula):
        if formula.upgrade_type == UpgradeType.FREE:
            self.communication_service.send_communication_for_free_upgrade(
                booking=self.crs_client.get_booking(booking.booking_id)
            )
