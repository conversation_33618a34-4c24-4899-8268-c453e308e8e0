class RoomTypeInventories(object):
    def __init__(self, room_type_inventories):
        self.room_type_inventories = room_type_inventories

    def get_maximum_occupancy_percent(self, total_inventory):
        return int(
            ((total_inventory - self._get_min_availability()) / total_inventory) * 100
        )

    def _get_min_availability(self):
        return min(
            [
                availability.get('actual_count')
                for availability in self.room_type_inventories
            ]
        )
