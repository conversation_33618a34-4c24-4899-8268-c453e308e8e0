# coding=utf-8
"""
extensions
"""
import logging
import uuid

from flask import g
from flask_migrate import Migrate
from flask_sqlalchemy import SignallingSession, SQLAlchemy, event

logger = logging.getLogger(__name__)


class DynamicRoutingSQLAlchemy(SQLAlchemy):
    """
    Dynamic routing sqlalchemy
    """

    # def create_scoped_session(self, options=None):
    #     """Helper factory method that creates a scoped session.  It
    #     internally calls :meth:`create_session`.
    #     """
    #     if options is None:
    #         options = {}
    #     scopefunc = options.pop('scopefunc', None)
    #     return orm.scoped_session(partial(self.create_session, options),
    #                               scopefunc=scopefunc)

    def create_session(self, options=None):
        """
        Create session
        :param options:
        :return:
        """
        app = self.get_app()
        engine = self.engine
        # if hasattr(request, 'bind_name'):
        #     engine = self.get_engine(app=app, bind=request.bind_name)
        binds = dict.fromkeys(self.get_binds(app=app).keys(), engine)
        return SignallingSession(db=self, app=None, bind=engine, binds=binds, **options)


db = DynamicRoutingSQLAlchemy(session_options={"autoflush": False})
migrate = Migrate(db=db, directory='inventory_management/migrations')


def log_on_connection_checkout(dbapi_conn, connection_rec, connection_proxy):
    # This is created and set here so that checkin can match checkout.
    # Request id is cleared by the time `log_on_connection_checkin` is called
    g.crs_connection_id = uuid.uuid1()
    logger.info(
        "SQLAlchemy connection checkout: %s, uuid %s", dbapi_conn, g.crs_connection_id
    )


def log_on_connection_checkin(dbapi_connection, connection_record):
    logger.info(
        "SQLAlchemy connection checkin %s, uuid %s",
        dbapi_connection,
        g.get('crs_connection_id'),
    )
    g.crs_connection_id = None


def register_sqlalchemy_pool_listeners():
    # Use this to create hooks on connection checkout and checkin if needed
    event.listen(db.engine, 'checkout', log_on_connection_checkout)
    event.listen(db.engine, 'checkin', log_on_connection_checkin)
