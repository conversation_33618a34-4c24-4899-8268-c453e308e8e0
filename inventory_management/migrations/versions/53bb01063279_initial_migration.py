"""Initial migration.

Revision ID: 53bb01063279
Revises:
Create Date: 2020-04-02 13:04:40.340539

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '53bb01063279'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'occupancy_config',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('config_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('higher_room_max_occupancy', sa.Integer(), nullable=False),
        sa.Column('higher_room_min_occupancy', sa.Integer(), nullable=False),
        sa.Column('lower_room_max_occupancy', sa.Integer(), nullable=False),
        sa.Column('lower_room_min_occupancy', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('config_id'),
    )
    op.create_table(
        'room_type',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('room_type_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('room_type_level', sa.Integer(), nullable=False),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('room_type_name', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('room_type_id'),
        sa.UniqueConstraint('room_type_level'),
        sa.UniqueConstraint('room_type_name'),
    )
    op.create_table(
        'time_config',
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('time_range_start', sa.Time(), nullable=False),
        sa.Column('time_range_end', sa.Time(), nullable=False),
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('cron_interval_hours', sa.Integer(), nullable=False),
        sa.Column('config_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('abw_start', sa.Integer(), nullable=False),
        sa.Column('abw_end', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('config_id'),
    )
    op.create_table(
        'available_room_type_upgrade',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'room_type_upgrade_id', sa.Integer(), autoincrement=True, nullable=False
        ),
        sa.Column('lower_room_type', sa.Integer(), nullable=False),
        sa.Column('higher_room_type', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ['higher_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.ForeignKeyConstraint(
            ['lower_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.PrimaryKeyConstraint('room_type_upgrade_id'),
    )
    op.create_table(
        'custom_price_slab',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('slab_value', sa.Integer(), nullable=False),
        sa.Column('slab_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('lower_room_type', sa.Integer(), nullable=False),
        sa.Column('higher_room_type', sa.Integer(), nullable=False),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('hotel_id', sa.String(), nullable=False),
        sa.Column('slab_type', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ['higher_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.ForeignKeyConstraint(
            ['lower_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.PrimaryKeyConstraint('hotel_id', 'slab_id'),
    )
    op.create_table(
        'default_price_slab',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('lower_room_type', sa.Integer(), nullable=False),
        sa.Column('higher_room_type', sa.Integer(), nullable=False),
        sa.Column('slab_value', sa.Integer(), nullable=False),
        sa.Column('slab_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('slab_type', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ['higher_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.ForeignKeyConstraint(
            ['lower_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.PrimaryKeyConstraint('slab_id'),
    )
    op.create_table(
        'optin_status',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('hotel_id', sa.String(), nullable=False),
        sa.Column('optin_status_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('lower_room_type', sa.Integer(), nullable=False),
        sa.Column('higher_room_type', sa.Integer(), nullable=False),
        sa.Column('upgrade_type', sa.String(), nullable=False),
        sa.Column('optin_status', sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ['higher_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.ForeignKeyConstraint(
            ['lower_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.PrimaryKeyConstraint('hotel_id', 'optin_status_id'),
    )
    op.create_table(
        'processed_room_upgrade',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('lower_room_type', sa.Integer(), nullable=False),
        sa.Column('higher_room_type', sa.Integer(), nullable=False),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('upgrade_amount', sa.DECIMAL(), nullable=False),
        sa.Column('upgrade_status', sa.String(), nullable=False),
        sa.Column('booking_id', sa.String(), nullable=False),
        sa.Column('room_stay_id', sa.String(), nullable=False),
        sa.Column('hotel_id', sa.String(), nullable=False),
        sa.Column('upgrade_type', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ['higher_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.ForeignKeyConstraint(
            ['lower_room_type'],
            ['room_type.room_type_id'],
        ),
        sa.PrimaryKeyConstraint('booking_id', 'room_stay_id', 'hotel_id'),
    )
    op.create_table(
        'room_upgrade_formula',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('formula_id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('time_config_id', sa.Integer(), nullable=False),
        sa.Column('occupancy_config_id', sa.Integer(), nullable=False),
        sa.Column('room_type_upgrade_id', sa.Integer(), nullable=True),
        sa.Column('higher_room_type_fill_percent', sa.Integer(), nullable=False),
        sa.Column('upgrade_type', sa.String(), nullable=False),
        sa.Column('slab_type', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ['occupancy_config_id'],
            ['occupancy_config.config_id'],
        ),
        sa.ForeignKeyConstraint(
            ['room_type_upgrade_id'],
            ['available_room_type_upgrade.room_type_upgrade_id'],
        ),
        sa.ForeignKeyConstraint(
            ['time_config_id'],
            ['time_config.config_id'],
        ),
        sa.PrimaryKeyConstraint('formula_id'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('room_upgrade_formula')
    op.drop_table('processed_room_upgrade')
    op.drop_table('optin_status')
    op.drop_table('default_price_slab')
    op.drop_table('custom_price_slab')
    op.drop_table('available_room_type_upgrade')
    op.drop_table('time_config')
    op.drop_table('room_type')
    op.drop_table('occupancy_config')
    # ### end Alembic commands ###
