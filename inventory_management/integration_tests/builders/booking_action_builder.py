class CancelBookingBuilder(object):
    def __init__(self, version):
        self.resource_version = version
        self.data = CancelBookingBuilder.Data().__dict__

    class Data(object):
        def __init__(self):
            self.action_type = "cancel"
            self.payload = CancelBookingBuilder.Data.Payload().__dict__

        class Payload(object):
            def __init__(self):
                self.cancel = CancelBookingBuilder.Data.Payload.Cancel().__dict__

            class Cancel(object):
                def __init__(self):
                    self.cancellation_reason = "testing"
