from inventory_management.integration_tests.builders import common_request_builder
from inventory_management.integration_tests.builders.common_request_builder import (
    booking_repo,
)
from inventory_management.integration_tests.config import common_config, sheet_names
from inventory_management.integration_tests.utilities import excel_utils
from inventory_management.integration_tests.utilities.common_utils import sanitize_blank
from prometheus.domain.booking.services.booking_id_generator import BookingIdGenerator


class CreateBookingRequest(object):
    def __init__(self, sheet_name, test_case_id, hotel_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = _BookingData(test_data, hotel_id).__dict__


class EditCustomerRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = common_request_builder.Customer(test_data).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class _BookingData(object):
    def __init__(self, test_data, hotel_id):
        owner_data = excel_utils.get_test_case_data(
            sheet_names.CUSTOMER_DATA_SHEET_NAME, test_data[0]['booking_owner']
        )
        self.booking_owner = common_request_builder.Customer(owner_data[0]).__dict__
        self.reference_number = str(BookingIdGenerator.rand_x_digit_num())
        self.hotel_id = hotel_id
        self.status = test_data[0]['status']
        room_test_data = excel_utils.get_test_case_data(
            sheet_names.ADD_ROOM_STAY_SHEET_NAME, test_data[0]['room_stays']
        )
        self.room_stays = []
        for room_data in room_test_data:
            room_stay = common_request_builder.RoomStay(room_data).__dict__
            self.room_stays.append(room_stay)
        self.source = common_request_builder.Source(test_data[0]).__dict__
        if sanitize_blank(test_data[0]['payments']) is not None:
            self.payments = []
            payment_data = excel_utils.get_test_case_data(
                sheet_names.ADD_PAYMENT_SHEET_NAME, test_data[0]['payments']
            )
            self.payments.append(
                common_request_builder.Payment(payment_data[0]).__dict__
            )
