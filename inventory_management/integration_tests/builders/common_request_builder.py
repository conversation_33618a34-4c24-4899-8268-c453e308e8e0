from inventory_management.integration_tests.config import common_config, sheet_names
from inventory_management.integration_tests.utilities import common_utils, excel_utils
from inventory_management.integration_tests.utilities.common_utils import (
    increment_date,
    sanitize_blank,
    sanitize_test_data,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories import AddonRepository, BookingRepository


def booking_repo():
    return BookingRepository()


def bill_repo():
    return BillRepository()


def addon_repo():
    return AddonRepository()


class RoomStay(object):
    def __init__(self, test_data):
        self.checkin_date = increment_date(
            int(test_data['checkin_date']), common_config.CHECK_IN_TIME_ZONE
        )
        self.checkout_date = increment_date(
            int(test_data['checkout_date']), common_config.CHECKOUT_TIME_ZONE
        )
        self.room_type_id = common_utils.get_room_type_id(test_data['room_type_id'])
        self.type = test_data['type']
        guest_test_data = excel_utils.get_test_case_data(
            sheet_names.ADD_GUEST_STAY_SHEET_NAME, test_data['GuestStay']
        )
        self.guest_stays = []
        for guest_data in guest_test_data:
            guest_stay = GuestStay(guest_data).__dict__
            self.guest_stays.append(guest_stay)
        price_test_data = excel_utils.get_test_case_data(
            sheet_names.ADD_PRICES_SHEET_NAME, test_data['Prices']
        )
        self.prices = []
        for price_data in price_test_data:
            price = Prices(price_data).__dict__
            self.prices.append(price)


class GuestStay(object):
    def __init__(self, guest_data):
        self.checkin_date = increment_date(
            int(guest_data['checkin_date']), common_config.CHECK_IN_TIME_ZONE
        )
        self.checkout_date = increment_date(
            int(guest_data['checkout_date']), common_config.CHECKOUT_TIME_ZONE
        )
        self.age_group = guest_data['age_group']
        if sanitize_blank(guest_data['guest_details']) is not None:
            customer_data = excel_utils.get_test_case_data(
                sheet_names.CUSTOMER_DATA_SHEET_NAME, guest_data['guest_details']
            )
            self.guest = Customer(customer_data[0]).__dict__


class Customer(object):
    def __init__(self, owner_data):
        self.age = sanitize_blank(owner_data['age'])
        self.id_proof = {
            'id_kyc_url': sanitize_blank(owner_data['id_kyc_url']),
            'id_number': sanitize_blank(owner_data['id_proof_type']),
        }
        self.address = sanitize_blank(Address(owner_data).__dict__)
        self.first_name = owner_data['first_name']
        self.last_name = sanitize_blank(owner_data['last_name'])
        self.gender = sanitize_blank(owner_data['gender'])
        self.email = owner_data['email']
        self.phone = {
            'country_code': sanitize_blank(owner_data['country_code']),
            'number': sanitize_blank(owner_data['number']),
        }
        self.gst_details = sanitize_test_data(GstinDetails(owner_data).__dict__)


class Address(object):
    def __init__(self, address_data):
        self.city = sanitize_blank(address_data['city'])
        self.country = sanitize_blank(address_data['country'])
        self.field_1 = sanitize_blank(address_data['field_1'])
        self.field_2 = sanitize_blank(address_data['field_2'])
        self.pincode = sanitize_blank(address_data['pincode'])
        self.state = sanitize_blank(address_data['state'])


class GstinDetails(object):
    def __init__(self, gstin_data):
        self.gstin_num = sanitize_test_data(gstin_data['gstin_num'])
        self.legal_name = sanitize_test_data(gstin_data['legal_name'])
        self.address = sanitize_test_data(Address(gstin_data).__dict__)


class Prices(object):
    def __init__(self, prices_data):
        self.applicable_date = increment_date(
            int(prices_data['applicable_date']), common_config.CHECK_IN_TIME_ZONE
        )
        self.bill_to_type = sanitize_blank(prices_data['bill_to_type'])
        self.type = sanitize_blank(prices_data['type'])
        self.pretax_amount = sanitize_blank(prices_data['pretax_amount'])
        self.posttax_amount = sanitize_blank(prices_data['posttax_amount'])


class Source(object):
    def __init__(self, source_data):
        self.application_code = sanitize_blank(source_data['application_code'])
        self.channel_code = sanitize_blank(source_data['channel_code'])
        self.subchannel_code = sanitize_blank(source_data['subchannel_code'])


class Payment(object):
    def __init__(self, payment_data):
        self.amount = str(payment_data['amount'])
        self.date_of_payment = increment_date(
            int(sanitize_blank(payment_data['date_of_payment']))
        )
        self.paid_by = sanitize_blank(payment_data['paid_by'])
        self.paid_to = sanitize_blank(payment_data['paid_to'])
        self.payment_channel = sanitize_blank(payment_data['payment_channel'])
        self.payment_ref_id = sanitize_blank(payment_data['payment_ref_id'])
        self.payment_type = sanitize_blank(payment_data['payment_type'])
        self.status = sanitize_blank(payment_data['status'])
        self.comment = sanitize_blank(payment_data['comment'])
        self.payment_mode = sanitize_blank(payment_data['payment_mode'])
        self.payment_mode_sub_type = sanitize_blank(
            payment_data['payment_mode_sub_type']
        )
