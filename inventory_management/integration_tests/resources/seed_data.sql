INSERT INTO public.room_type (crs_id, level, name)
	VALUES ('RT01', 1, 'acacia'), ('RT02', 2, 'oak'), ('RT03', 3, 'maple'), ('RT04', 4, 'mahogany');
INSERT INTO public.default_price_slab (slab_value, slab_type, lower_room_type_id, higher_room_type_id)
	VALUES (299, 'higher', 1, 2), (199, 'lower', 1, 2),
		   (499, 'higher', 1, 3), (299, 'lower', 1, 3),
		   (699, 'higher', 1, 4), (399, 'lower', 1, 4),
		   (399, 'higher', 2, 3), (199, 'lower', 2, 3),
		   (499, 'higher', 2, 4), (299, 'lower', 2, 4),
		   (399, 'higher', 3, 4), (199, 'lower', 3, 4);
INSERT INTO public.available_room_type_upgrade (lower_room_type_id, higher_room_type_id, upgrade_priority)
	VALUES (2, 3, 1), (3, 4, 2), (1, 2, 3), (2, 4, 4), (1, 3, 5), (1, 4, 6);
INSERT INTO public.occupancy_config (higher_room_max_occupancy, higher_room_min_occupancy, lower_room_max_occupancy, lower_room_min_occupancy)
	VALUES (100, 50, 100, 100), (49, 0, 100, 100),
		   (100, 50, 99, 80), (49, 0, 99, 80),
		   (100, 50, 79, 0), (49, 0, 79, 0);
INSERT INTO public.time_config (time_range_start, time_range_end, cron_interval_hours, abw_start, abw_end, last_run)
	VALUES ('16:00', '23:00', 1, 0, 0, now()),
		   ('12:00', '15:59', 1, 0, 0, now()),
		   ('06:00', '11:59', 1, 0, 0, now()),
		   ('18:00', '23:00', 1, 1, 1, now()),
		   ('12:00', '17:59', 1, 1, 1, now()),
		   ('06:00', '23:00', 1, 2, 2, now()),
		   ('06:00', '23:00', 24, 2, 7, now());
INSERT INTO public.room_upgrade_formula (time_config_id, occupancy_config_id, higher_room_type_fill_percent, upgrade_type, slab_type)
	VALUES (1, 1, 100, 'free', 'higher'),	(1, 2, 100, 'free', 'lower'),	(1, 3, 100, 'paid', 'higher'),	(1, 4, 100, 'paid', 'lower'),	(1, 5, 100, 'paid', 'higher'),	(1, 6, 100, 'paid', 'lower'),
		   (2, 1, 75, 'free', 'higher'),	(2, 2, 100, 'free', 'lower'),	(2, 3, 100, 'paid', 'higher'),	(2, 4, 100, 'paid', 'lower'),	(2, 5, 100, 'paid', 'higher'),	(2, 6, 100, 'paid', 'lower'),
		   (3, 1, 30, 'free', 'higher'),	(3, 2, 50, 'free', 'lower'),	(3, 3, 100, 'paid', 'higher'),	(3, 4, 100, 'paid', 'lower'),	(3, 5, 100, 'paid', 'higher'),	(3, 6, 100, 'paid', 'lower'),
		   (4, 1, 10, 'free', 'higher'),	(4, 2, 30, 'free', 'higher'),	(4, 3, 100, 'paid', 'higher'),	(4, 4, 100, 'paid', 'higher'),	(4, 5, 100, 'paid', 'higher'),	(4, 6, 100, 'paid', 'lower'),
		   (5, 1, 100, 'paid', 'higher'),	(5, 2, 15, 'free', 'higher'),	(5, 3, 100, 'paid', 'higher'),	(5, 4, 100, 'paid', 'higher'),	(5, 5, 100, 'paid', 'higher'),	(5, 6, 100, 'paid', 'lower'),
		   (6, 1, 100, 'paid', 'higher'),	(6, 2, 100, 'paid', 'lower'),	(6, 3, 100, 'paid', 'higher'),	(6, 4, 100, 'paid', 'higher'),	(6, 5, 100, 'paid', 'higher'),	(6, 6, 100, 'paid', 'lower'),
		   (7, 1, 100, 'paid', 'higher'),	(7, 2, 100, 'paid', 'lower'),	(7, 3, 100, 'paid', 'higher'),	(7, 4, 100, 'paid', 'higher'),	(7, 5, 100, 'paid', 'higher'),	(7, 6, 100, 'paid', 'higher');