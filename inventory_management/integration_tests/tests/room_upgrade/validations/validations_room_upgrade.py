from datetime import datetime, timedelta

from inventory_management.integration_tests.config import sheet_names
from inventory_management.integration_tests.external_clients.crs_client import CrsClient
from inventory_management.integration_tests.utilities.common_utils import assert_
from inventory_management.integration_tests.utilities.excel_utils import (
    get_test_case_data,
)


class ValidationRoomUpgrade(object):
    def __init__(self, test_case_id, hotel_id):
        self.hotel_id = hotel_id
        # j<PERSON><PERSON> timezone is different(5 hrs 30 min ahead), if you are running this in local,
        # comment this timedelta code
        current_time = datetime.now() + timedelta(hours=5, minutes=30)
        today6pm = current_time.replace(hour=18, minute=0, second=0, microsecond=0)
        today12am = current_time.replace(hour=23, minute=59, second=0, microsecond=0)
        today12pm = current_time.replace(hour=12, minute=0, second=0, microsecond=0)
        today4pm = current_time.replace(hour=16, minute=0, second=0, microsecond=0)
        today6am = current_time.replace(hour=6, minute=0, second=0, microsecond=0)
        if today6am <= current_time < today12pm:
            self.validate_test_data = get_test_case_data(
                sheet_names.VALIDATE_6AMto12PM_SHEET_NAME, test_case_id
            )
        elif today12pm <= current_time < today4pm:
            self.validate_test_data = get_test_case_data(
                sheet_names.VALIDATE_12PMto4PM_SHEET_NAME, test_case_id
            )
        elif today6pm <= current_time < today12am:
            self.validate_test_data = get_test_case_data(
                sheet_names.VALIDATE_6PMto12AM_SHEET_NAME, test_case_id
            )

    def validate_upgrade(self):
        for validate_data in self.validate_test_data:
            first_name = validate_data['FirstName']
            hotel_id = self.hotel_id
            expected_room_type_id = validate_data['expected_room_type_id']
            response = CrsClient().booking_search(hotel_id, first_name).json()
            print("Expected_room_id: %s ", expected_room_type_id)
            print(
                "response_room_type_id: %s ",
                response['data']['bookings'][0]['room_stays'][0]['room_type_id'],
            )
            assert (
                response['data']['bookings'][0]['room_stays'][0]['room_type_id']
                in expected_room_type_id
            )
