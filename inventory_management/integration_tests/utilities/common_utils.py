from datetime import date, timedelta

from prometheus.integration_tests.config import common_config

# returns the date_time for the given day with timezone
from prometheus.integration_tests.config.common_config import ROOM_TYPE_MAP
from shared_kernel.extensions import db


def increment_date(days, timezone=None):
    if timezone is None:
        return return_date(days).strftime(common_config.DEFAULT)
    else:
        return return_date(days).strftime(timezone)


# returns only the date for the given day
def return_date(days):
    return date.today() + timedelta(days=int(days))


# todo: with time completely replace sanitize_blank method with sanitize_test_data
# sanitize the values for blank fields from excel
def sanitize_blank(value):
    if (
        value == ''
        or value == 'null'
        or value == {}
        or value == 'BLANK'
        or value == 'NULL'
    ):
        return None
    else:
        return value


# interpret the values for NULL/EMPTY/blank fields from excel
def sanitize_test_data(data):
    if data == '' or data == 'null':
        return None
    elif data == 'NULL':
        return 'NULL_KEY'
    elif data == 'EMPTY':
        return ''
    elif isinstance(data, dict):
        if all(value is None for value in data.values()):
            return None
        elif all(value == 'NULL_KEY' or value is None for value in data.values()):
            return 'NULL_KEY'
        else:
            return data
    else:
        return data


# for deleting the null values and empty dict from object passed
def del_none(dict_object):
    for key, value in list(dict_object.items()):
        if isinstance(value, dict):
            del_none(value)
        elif isinstance(value, list):
            for val in value:
                if isinstance(val, dict):
                    del_none(val)
        if value == {} or value is None:
            del dict_object[key]
        if value == 'NULL_KEY':
            dict_object[key] = None
    return dict_object


# from room type name to room type id
def get_room_type_id(room_type):
    return ROOM_TYPE_MAP[room_type.lower()]


def assert_(actual_value, expected_value, failure_message=None):
    assert actual_value == expected_value, (
        str(failure_message)
        + ". ACTUAL: "
        + str(actual_value)
        + " EXPECTED: "
        + str(expected_value)
    )


def query_execute(query):
    db.session.execute(query)
    db.session.commit()
