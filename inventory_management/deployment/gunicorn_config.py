# coding=utf-8
"""
gunicorn config
"""
# pylint: skip-file
import os

from psycogreen.gevent import patch_psycopg

bind = '0.0.0.0:8000'

workers = 5 if os.environ.get('APP_ENV') in ('production', 'prod') else 2

worker_class = 'gevent'

worker_connections = 20
timeout = 30

# pidfile = '/var/run/bb8_gunicorn.pid'
#
reload = False

accesslog = '/var/log/ims/gunicorn_access.log'
errorlog = '/var/log/ims/gunicorn_error.log'
access_log_format = '{"remote_ip":"%(h)s","request_id":"%({X-Request-Id}i)s","response_code":"%(s)s","request_method":"%(m)s","request_path":"%(U)s","request_querystring":"%(q)s","request_timetaken":"%(D)s","response_length":"%(B)s"}'


loglevel = 'info'

proc_name = 'ims'


def post_fork(server, worker):
    """

    :param server:
    :param worker:
    :return:
    """
    # patch psycopg2 for gevent compatibility
    patch_psycopg()
