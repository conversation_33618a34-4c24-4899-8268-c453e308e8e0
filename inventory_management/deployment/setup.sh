#!/bin/bash
  
set -e
set -x

ENV="$1"
export VERSION="$2"
export APP="$3"
TARGET="$4"

echo "Using env : $ENV"
echo "Current build directory: $BUILD_DIR"

if [[ -z "${BUILD_DIR}" ]]; then
  echo "Please set BUILD_DIR env variable, which should be path where code will be checked out to create build"
  exit 1
fi

if [ "$ENV" = "staging" ]; then
    source $BUILD_DIR/$APP/envrepo/ims/build_env/staging
    if [ $TARGET = "app" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml up -d ims-apiservice

    elif [ $TARGET = "upgrade-cron" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/stag-compose.yml up -d room-upgrade-worker
    fi

elif [ "$ENV" = "production" ]; then
    source $BUILD_DIR/$APP/envrepo/ims/build_env/prod
    if [ $TARGET = "app" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml up -d ims-apiservice

    elif [ $TARGET = "upgrade-cron" ]; then
        docker-compose -f $BUILD_DIR/$APP/coderepo/docker/compose/prod-compose.yml up -d room-upgrade-worker
    fi
fi
