from functools import wraps

from inventory_management.extensions import db


def handle_db_commits(func):
    """
    Handle DB commits
    :param func:
    :return:
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        try:
            r_val = func(*args, **kwargs)
            db.session.commit()
            return r_val
        except Exception as e:
            db.session.rollback()
            raise e

    return wrapper
