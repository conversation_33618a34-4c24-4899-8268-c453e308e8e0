service: prometheus-reports

frameworkVersion: '2'


provider:
  name: aws
  runtime: python3.9
  stage: staging
  region: ${file(serverless/config/${opt:stage,self:provider.stage}/aws.js):region}
  memorySize: 2048
  timeout: 300
  lambdaHashingVersion: ********
  deploymentBucket:
    name: ${self:custom.deploymentBucketName.${self:custom.stage}}
    maxPreviousDeploymentArtifacts: 10
    blockPublicAccess: true
  role: !Sub 'arn:aws:iam::${AWS::AccountId}:role/prometheus-lambda-role'
  vpc:
    securityGroupIds:
      ${file(serverless/config/${opt:stage,self:provider.stage}/vpc.js):security}
    subnetIds:
      ${file(serverless/config/${opt:stage,self:provider.stage}/vpc.js):subnets}
  environment:
    AWS_S3_BUCKET_NAME: ${self:custom.secrets.INTOUCH_REPORTS_S3_BUCKET}
    AWS_S3_REGION: ${self:custom.secrets.INTOUCH_S3_REGION}
    TENANT_SERVICE_HOST: ${self:custom.secrets.TENANT_SERVICE_HOST}
    DISABLE_LOGGING: 'true'
    APP_NAME: crs
    CLUSTER_IDENTIFIER: ${self:custom.secrets.CLUSTER_IDENTIFIER}
    LANG: C.UTF-8
    APP_ENV: ${self:custom.secrets.APP_ENV}
    FLASK_APP: 'crs_app:app'
    BUILD_NUMBER: '1443'
    LOG_ROOT: '/tmp/'
    AWS_SECRET_PREFIX: 'apps/crs'
    SENTRY_DSN: 'https://<EMAIL>/5941345'

custom:
  secretsFilePathPrefix: serverless
  secrets: ${file(serverless/secrets.${opt:stage,self:provider.stage}.yml)}
  requirementsLayer: ${file(serverless/config/${opt:stage,self:provider.stage}/layer.js):requirementsLayer}
  sftpLayer: ${file(serverless/config/${opt:stage,self:provider.stage}/layer.js):sftpLayer}
  pyLibLayer: ${file(serverless/config/${opt:stage,self:provider.stage}/layer.js):pyLibLayer}
  psycopg2Layer: ${file(serverless/config/${opt:stage,self:provider.stage}/layer.js):psycopg2Layer}
  stage: ${opt:stage, self:provider.stage}
  deploymentBucketName:
    prod: 'serverless-interface-p-deploy-s3'
    staging: 'serverless-interface-s-deploy-s3'

package:
  individually: true
  include:
    - ./prometheus/**
    - ./shared_kernel/**
    - ./ths_common/**

  exclude:
    - ./node_modules/**
    - ./env/**
    - ./build/**
    - ./dist/**
    - ./.git/**
    - ./prometheus.log
    - ./scripts/**
    - ./serverless/**
    - ./prometheus/itests/**
    - ./prometheus/integration_tests/**
    - ./prometheus/tests/**


functions:
  reservation-reports:
    handler: intouch_reservation.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):intouchTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.pyLibLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

  profile-reports:
    handler: intouch_profile.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):intouchTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayerName}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

  transaction-reports:
    handler: intouch_transaction.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):intouchTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.pyLibLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

  code-reports:
    handler: intouch_code.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):intouchTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

  inventory-reports:
    handler: intouch_inventory.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):intouchTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

  acdc-reports:
    handler: acdc_arrival_list.lambda_handler
    module: reports
    events:
      - sns:
          arn: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):sns}
          filterPolicy:
            tenant_id: ${file(serverless/config/${opt:stage,self:provider.stage}/events.js):acdcTenantFilter}
    layers:
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.requirementsLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.sftpLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.pyLibLayer}
      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:${self:custom.psycopg2Layer}

plugins:
  - serverless-python-requirements
  - serverless-secrets-plugin
