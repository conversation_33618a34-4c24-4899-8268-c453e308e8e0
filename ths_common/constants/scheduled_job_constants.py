import enum

from ths_common.constants.base_enum import BaseEnum


class JobStatus(BaseEnum):
    CREATED = 'created'
    PROCESSING = 'processing'
    FAILED = 'failed'
    FINISHED = 'finished'
    RESCHEDULED = 'rescheduled'
    PICKED_FOR_EXECUTION = 'picked_for_execution'


class JobName(enum.Enum):
    INVOICE_TEMPLATE_GENERATION = "generate_and_upload_invoice_template"

    PERFORM_NIGHT_AUDIT_ASYNC_JOB_NAME = "perform_night_audit"

    BULK_SYNC_INVENTORY_ASYNC_JOB_NAME = "sync_inventories"
    CREDIT_NOTE_UPLOAD_ASYNC_JOB_NAME = "credit_note_template_generation"
    PAYMENT_RECEIPT_UPLOAD_ASYNC_JOB_NAME = "payment_receipt_generation"
    PAYMENT_VOUCHER_UPLOAD_SEND_ASYNC_JOB_NAME = "payment_voucher_generation"
    SEND_BOOKING_CONFIRMATION_VOUCHER = "send_booking_confirmation_voucher"
    SEND_PAYMENT_RECEIPT = "send_payment_receipt"
    SOFT_BOOKING_CANCELLATION_JOB_NAME = "soft_booking_cancellation"
    PAYMENT_LINK_COMMUNICATION = 'payment_link_communication'
    GENERATE_EREGCARD_URL = 'generate_eregcard_url'
    REFRESH_INVOICES = 'refresh_invoices'

    GENERATE_TRIAL_BALANCE_REPORT = 'generate_trial_balance_report'
    GENERATE_FLASH_MANAGER_REPORT = 'generate_flash_manager_report'
    WEB_CHECKIN_REMINDER = 'web_checkin_reminder'
    WEB_CHECKIN_COMMUNICATION = 'web_checkin_communication'
    WEB_CHECKIN_REJECTION_COMMUNICATION = 'web_checkin_rejection_communication'
    EINVOICE_REPORT_EMAILER = 'einvoice_report_emailer'
    MARVIN_REPORT_GENERATOR_JOB_NAME = 'marvin_report_generation'
    INVOICE_REPORT_GENERATOR_JOB_NAME = 'invoice_report_generation'

    TRIGGER_SEGMENT_EVENT = 'trigger_segment_event'
    EINVOICE_SUBMISSION = 'einvoice_submission'
    CRITICAL_TASK_REMINDER = 'critical_task_reminder'

    BOOKING_MIGRATION = 'booking_migration'
    LOCK_EREGCARD = 'lock_eregcard'
    SCHEDULE_INVOICE_EMAIL_ON_CHECKOUT = 'schedule_invoice_email_on_checkout'
    SCHEDULE_BOOKING_CONFIRMATION_IN_B2B = 'schedule_booking_confirmation_in_b2b'
    SCHEDULE_PAYOUT_LINK_COMMUNICATION = 'schedule_payout_link_communication'
    SCHEDULE_DIRECT_REFUND_COMMUNICATION = 'schedule_direct_refund_communication'
    SCHEDULE_INCOMPLETE_BOOKING_COMMUNICATION = (
        'schedule_incomplete_booking_communication'
    )
    INCOMPLETE_BOOKING_REMINDER = 'incomplete_booking_reminder'
    SCHEDULE_PAYMENT_FAILURE_COMMUNICATION = 'schedule_payment_failure_communication'
    SCHEDULE_PAYOUT_LINK_NOT_APPROVED_COMMUNICATION = (
        'schedule_payout_link_not_approved_communication'
    )
    AUTO_REFUND_BLACKLISTED_USER_COMMUNICATION = (
        'auto_refund_blacklisted_user_communication'
    )
    SCHEDULE_PARTIAL_REFUND_COMMUNICATION = 'schedule_partial_refund_communication'
    RELEASE_INVENTORY = "release_soft_block_inventory"
    FULFILL_VIRTUAL_INVENTORY_BLOCKS = "virtual_inventory_block_fulfillment"
    SCHEDULE_FINANCIAL_DATA_SYNC = 'schedule_financial_data_sync'


JOB_PRIORITY = {
    JobName.INVOICE_TEMPLATE_GENERATION.value: 1,
    JobName.PERFORM_NIGHT_AUDIT_ASYNC_JOB_NAME.value: 1,
    JobName.BULK_SYNC_INVENTORY_ASYNC_JOB_NAME.value: 3,
    JobName.CREDIT_NOTE_UPLOAD_ASYNC_JOB_NAME.value: 3,
    JobName.PAYMENT_RECEIPT_UPLOAD_ASYNC_JOB_NAME.value: 3,
    JobName.PAYMENT_VOUCHER_UPLOAD_SEND_ASYNC_JOB_NAME.value: 3,
    JobName.SOFT_BOOKING_CANCELLATION_JOB_NAME.value: 3,
    JobName.PAYMENT_LINK_COMMUNICATION.value: 3,
    JobName.GENERATE_EREGCARD_URL.value: 3,
    JobName.REFRESH_INVOICES.value: 3,
    JobName.BOOKING_MIGRATION.value: 3,
    JobName.SCHEDULE_INVOICE_EMAIL_ON_CHECKOUT.value: 3,
    JobName.SCHEDULE_PAYOUT_LINK_COMMUNICATION.value: 3,
    JobName.SCHEDULE_INCOMPLETE_BOOKING_COMMUNICATION.value: 3,
    JobName.SCHEDULE_FINANCIAL_DATA_SYNC.value: 3,
    JobName.SCHEDULE_PAYOUT_LINK_NOT_APPROVED_COMMUNICATION: 4,
    JobName.GENERATE_TRIAL_BALANCE_REPORT.value: 4,
    JobName.GENERATE_FLASH_MANAGER_REPORT.value: 4,
    JobName.WEB_CHECKIN_REMINDER.value: 4,
    JobName.WEB_CHECKIN_COMMUNICATION.value: 4,
    JobName.WEB_CHECKIN_REJECTION_COMMUNICATION.value: 4,
    JobName.EINVOICE_REPORT_EMAILER.value: 4,
    JobName.MARVIN_REPORT_GENERATOR_JOB_NAME.value: 4,
    JobName.INVOICE_REPORT_GENERATOR_JOB_NAME.value: 4,
    JobName.INCOMPLETE_BOOKING_REMINDER.value: 4,
    JobName.AUTO_REFUND_BLACKLISTED_USER_COMMUNICATION.value: 4,
    JobName.TRIGGER_SEGMENT_EVENT.value: 10,
    JobName.EINVOICE_SUBMISSION.value: 10,
    JobName.CRITICAL_TASK_REMINDER.value: 10,
    JobName.LOCK_EREGCARD.value: 99,
    JobName.SCHEDULE_PAYMENT_FAILURE_COMMUNICATION.value: 4,
    JobName.SCHEDULE_PARTIAL_REFUND_COMMUNICATION.value: 4,
    JobName.RELEASE_INVENTORY.value: 4,
}

JOB_SUCCESS_CODE = 200
JOB_FAILURE_CODE = 500
