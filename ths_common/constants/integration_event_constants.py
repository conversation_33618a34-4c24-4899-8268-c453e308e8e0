import enum


class IntegrationEventStatus(enum.Enum):
    UNPUBLISHED = 'unpublished'
    PUBLISHED = 'published'
    FAILED = 'failed'


class IntegrationEventType(enum.Enum):
    BOOKING_CREATED = 'booking.created'
    BOOKING_UPDATED = 'booking.updated'

    BILL_UPDATED = 'billing.updated'

    INVOICE_PREVIEW_CREATED = 'invoice.preview.created'
    INVOICE_PREVIEW_UPDATED = 'invoice.preview.updated'
    INVOICE_GENERATED = 'invoice.generated'
    INVOICE_UPDATED = 'invoice.updated'
    INVOICE_CANCELLED = 'invoice.cancelled'

    PAYMENT_ADDED = 'billing.payment.added'

    DNR_SET = 'inventory.dnr.set'
    DNR_UPDATED = 'inventory.dnr.updated'
    DNR_RELEASED = 'inventory.dnr.released'
    DNR_REMOVED = 'inventory.dnr.removed'
    ROOM_TYPE_INVENTORY_UPDATED = 'inventory.room_type.updated'
    ROOM_INVENTORY_UPDATED = 'inventory.room.updated'
    CREDIT_NOTE_GENERATED = "credit_note.generated"
    CREDIT_NOTE_UPDATED = "credit_note.updated"

    OVERFLOW_UPDATED = "overflow.updated"

    HOUSEKEEPING_RECORD_UPDATED = "housekeeping_record.updated"

    # POS EVENT
    ORDER_CREATED = 'order.created'
    ORDER_UPDATED = 'order.updated'
    ORDER_SENT_TO_KITCHEN = 'order.sent_to_kitchen'
    ORDER_BILL_SETTLED = 'order.bill_settled'

    NIGHT_AUDIT_SCHEDULED = 'night_audit.scheduled'
    NIGHT_AUDIT_COMPLETED = 'night_audit.completed'
    NIGHT_AUDIT_STARTED = 'night_audit.started'

    FUNDING_DETAILS_UPDATED = 'funding_details.updated'

    @property
    def routing_key(self):
        if self.value in (
            'booking.created',
            'booking.updated',
            'billing.updated',
            'billing.payment.added',
        ):
            return 'booking'
        elif self.value in (
            'inventory.dnr.set',
            'inventory.dnr.updated',
            'inventory.dnr.released',
            'inventory.dnr.removed',
            'inventory.room_type.updated',
            'inventory.room.updated',
            'housekeeping_record.updated',
        ):
            return 'inventory'
        elif self.value in (
            'invoice.preview.created',
            'invoice.preview.updated',
            'invoice.generated',
            'invoice.updated',
            'credit_note.generated',
            'credit_note.updated',
            'invoice.cancelled',
        ):
            return 'booking'
        elif self.value in ('overflow.updated',):
            return 'overflow'
        elif self.value in (
            'night_audit.scheduled',
            'night_audit.completed',
            'night_audit.started',
        ):
            return 'night_audit'
        elif self.value in (
            'order.created',
            'order.updated',
            'order.sent_to_kitchen',
            'order.bill_settled',
        ):
            return 'order'
        elif self.value in ('credit_shell_refund.created',):
            return 'credit_shell_refund'
        elif self.value in ('funding_details.updated',):
            return 'funding_details'
        else:
            raise ValueError(
                "Integration Event Type: {0} doesn't have a routing key associated".format(
                    self.value
                )
            )


class EntityName(enum.Enum):
    BOOKING_ENTITY = 'booking'
    BILL_ENTITY = 'bill'
