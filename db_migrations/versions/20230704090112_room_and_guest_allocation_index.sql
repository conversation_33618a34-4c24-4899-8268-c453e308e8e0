-- revision: '20230704090112_room_and_guest_allocation_index'
-- down_revision: '20230630171937_booking_and_billing'

-- upgrade
CREATE INDEX IF NOT EXISTS ix_room_allocation_modified_at_booking_id ON room_allocation (modified_at, booking_id);
CREATE INDEX IF NOT EXISTS ix_guest_allocation_booking_id_room_stay_id_guest_stay_id_guest ON guest_allocation (booking_id, room_stay_id, guest_stay_id, guest_id, guest_allocation_id);

-- downgrade
DROP INDEX ix_room_allocation_modified_at_booking_id;
DROP INDEX ix_guest_allocation_booking_id_room_stay_id_guest_stay_id_guest;
