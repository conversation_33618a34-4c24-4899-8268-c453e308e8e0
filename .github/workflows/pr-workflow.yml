name: release/main PR Workflow

on:
  push:
    branches: [release, main]
  pull_request:
    branches: [release, main]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false  # Continue other jobs even if one fails
      matrix:
        db_index: [1, 2, 3]  # Define indices for different builds

    env:
      DB_NAME: "crs_test_${{ matrix.db_index }}"  # Set the environment variable
      RUNNING_ON_GITHUB: True

    services:
      postgres:
        image: postgres
        env:
          POSTGRES_DB: ${{ env.DB_NAME }}  # Use the environment variable here
          POSTGRES_USER: crs_test_user
          POSTGRES_PASSWORD: crs_test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v2
      - uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: |
            ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
            ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}

      - name: Set up Python 3.9
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'

      - name: Install dependencies
        run: |
          pip install --upgrade pip==21.1.2
          cat << EOF > prometheus/deployment/requirements/deploy.txt
          git+ssh://**************/treebo-noss/flaskhealthcheck.git@main#egg=flaskhealthcheck
          git+ssh://**************/treebo-noss/treebo-common.git@support/3.2.0#egg=treebo-commons
          -r base.txt
          EOF
          pip install -r prometheus/deployment/requirements/dev.txt

      - name: Black formatter check
        run: |
          if [[ ${{ matrix.db_index }} == 1 ]]; then
            black prometheus --check
            black pos --check
            black shared_kernel --check
            black thsc --check
            black ths_common --check
            black inventory_management --check
          fi

      - name: Isort check
        run: |
          if [[ ${{ matrix.db_index }} == 1 ]]; then
            isort prometheus -s=prometheus/integration_tests --profile=black --check
            isort pos --profile=black --check
            isort shared_kernel --profile=black --check
            isort thsc --profile=black --check
            isort ths_common --profile=black --check
            isort inventory_management --profile=black --check
          fi

      - name: Unit Tests with Pytest
        run: |
          if [[ ${{ matrix.db_index }} == 1 ]]; then
            pytest -x prometheus/tests
          fi

      - name: ITests with Pytest
        run: |
          if [[ ${{ matrix.db_index }} == 1 ]]; then
            pytest -x prometheus/itests
          fi

      - name: Run Integration Tests with Pytest
        run: |
          if [[ ${{ matrix.db_index }} == 1 ]]; then
            pytest -x \
              prometheus/integration_tests/tests/Invoices/tests \
              prometheus/integration_tests/tests/billing/tests \
              prometheus/integration_tests/tests/cards/tests \
              prometheus/integration_tests/tests/cashier_module/tests \
              prometheus/integration_tests/tests/correctionmode/tests \
              prometheus/integration_tests/tests/credit_notes/tests \
              prometheus/integration_tests/tests/dnr/test \
              prometheus/integration_tests/tests/e_reg_card/tests \
              prometheus/integration_tests/tests/expense/tests \
              prometheus/integration_tests/tests/house_status/tests \
              prometheus/integration_tests/tests/housekeeping/tests \
              prometheus/integration_tests/tests/inventory/tests \
              prometheus/integration_tests/tests/invoice/tests \
              prometheus/integration_tests/tests/payment/tests \
              prometheus/integration_tests/tests/web_checkin/tests
          elif [[ ${{ matrix.db_index }} == 2 ]]; then
            pytest -x prometheus/integration_tests/tests/booking/tests-part-1
          elif [[ ${{ matrix.db_index }} == 3 ]]; then
            pytest -x prometheus/integration_tests/tests/booking/tests-part-2
          fi
