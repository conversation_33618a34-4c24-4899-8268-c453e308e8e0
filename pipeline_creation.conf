http://ci.treebo.com:8080/job/devops-p2-buildpipeline-creation/

Worker
========
APPNAME: crsworkers
PODNAME: crs
BUILD_REQ_FILE_NAME: requirements.txt
PYTHON_VERSION: 3.6.0
COMPOSE_REPO_NAME: prometheus
CONFIG_REPO_NAME: crs-envconfig
COMPOSE_BRANCH_NAME: develop (or whatever should be deployed)
CONFIG_BRANCH_NAME: master
LOADBALANCER_TYPE: None
ALB_INSTANCE_PORT: <leave empty>
HAPROXY_BACKEND_CLUSTER_NAME: <leave empty>
HAPROXY_BACKEND_DRAINING_PERIOD: <leave empty>
COMPOSER_TYPE: Docker_Composer
UNIT_TEST_CODE: coverage run --source prometheus -m pytest prometheus/tests --junitxml=nosetests.xml
SMOKE_TEST_CODE: < Fill in here >

APP_DEPLOY_COMMAND_PARAMETERS: worker




Backend api
=============
APPNAME: crsapi
PODNAME: crs
BUILD_REQ_FILE_NAME: requirements.txt
PYTHON_VERSION: 3.6.0
COMPOSE_REPO_NAME: prometheus
CONFIG_REPO_NAME: crs-envconfig
COMPOSE_BRANCH_NAME: develop (or whatever should be deployed)
CONFIG_BRANCH_NAME: master
LOADBALANCER_TYPE: ALB
ALB_INSTANCE_PORT: 8000
HAPROXY_BACKEND_CLUSTER_NAME: <leave empty>
HAPROXY_BACKEND_DRAINING_PERIOD: <leave empty>
COMPOSER_TYPE: Docker_Composer
UNIT_TEST_CODE: coverage run --source prometheus -m pytest prometheus/tests --junitxml=nosetests.xml
SMOKE_TEST_CODE: < Fill in here >

APP_DEPLOY_COMMAND_PARAMETERS: server
