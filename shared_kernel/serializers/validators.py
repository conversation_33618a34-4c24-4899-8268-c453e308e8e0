from marshmallow import ValidationError

from ths_common.utils.common_utils import gstin_regex, phone_regex


def validate_empty_string(value):
    if value == "":
        raise ValidationError("Field value cannot be blank")

    if value.isspace():
        raise ValidationError("Field value cannot consist of just whitespace")


def validate_phone_number(value):
    if not phone_regex.match(value):
        raise ValidationError("Please enter a valid phone number")


def validate_numeric_string(value):
    if not value.isdigit():
        raise ValidationError("Field value should be numeric")


def validate_gstin_number(value):
    if not value or not gstin_regex.match(value):
        raise ValidationError("Please enter a valid gstin number.")


def validate_positive_integer(value):
    if value <= 0:
        raise ValidationError("Field value should be greater than 0")
