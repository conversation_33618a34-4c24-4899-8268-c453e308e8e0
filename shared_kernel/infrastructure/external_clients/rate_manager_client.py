import logging

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from shared_kernel.infrastructure.external_clients.rate_manager_dtos import (
    RatePlanPackageDto,
    RatePlansDto,
    RoomGuardrailsDTO,
)
from shared_kernel.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from ths_common.constants.user_constants import UserType

logger = logging.getLogger(__name__)


@register_instance()
class RateManagerClient(BaseExternalClient):
    page_map = {
        'get_rate_plan_details': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/rate-manager/v1/rate-plans?rate_plan_ids={rate_plan_ids}",
        ),
        'get_package_details': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/rate-manager/v1/packages/{package_id}",
        ),
        'get_rate_plans_by_property_id': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/rate-manager/v1/rate-plans?property_id={property_id}",
        ),
        'get_rate_plans_by_property_and_codes': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/rate-manager/v1/rate-plans?property_id={property_id}&short_codes={short_codes}",
        ),
        'get_packages_by_property_id': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/rate-manager/v1/packages?property_id={property_id}",
        ),
        'get_rooms_guardrail': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/v1/applicable-guardrail-bounds",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_rate_manager_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        headers['X-User-Type'] = UserType.BACKEND_SYSTEM.value
        return headers

    def get_rate_plans_data(self, rate_plan_ids):
        page_name = "get_rate_plan_details"
        url_params = dict(rate_plan_ids=",".join(rate_plan_ids))
        logger.info(
            f"Making rate manager call with page_name: {page_name}, url_param: {url_params}"
        )
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Rate Manager API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response.get('data')

    def get_package_data(self, package_id):
        page_name = "get_package_details"
        url_params = dict(package_id=package_id)
        response = self.make_call(page_name, url_parameters=url_params)
        logger.info(
            f"Making rate manager call with page_name: {page_name}, url_param: {url_params}"
        )
        if not response.is_success():
            raise Exception(
                "Rate Manager API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response.get('data')

    def get_rate_plans(self, property_id, short_codes=None):
        response = self._get_rate_plan_data(property_id, short_codes=short_codes)
        return [
            RatePlansDto.create_from_payload(rate_plan)
            for rate_plan in response.json_response['data']['rate_plans']
        ]

    def _get_rate_plan_data(self, property_id, short_codes=None):
        page_name = "get_rate_plans_by_property_id"
        url_params = dict(property_id=property_id)
        if short_codes:
            url_params["short_codes"] = short_codes
            page_name = "get_rate_plans_by_property_and_codes"
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "Rate Manager API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response

    def get_packages_by_property_id(self, property_id):
        response = self._get_package_data(property_id)
        return [
            RatePlanPackageDto.create_from_payload(package)
            for package in response.json_response['data']['packages']
        ]

    def _get_package_data(self, property_id):
        page_name = "get_packages_by_property_id"
        url_params = dict(property_id=property_id)
        response = self.make_call(page_name, url_parameters=url_params)
        logger.info(
            f"Making rate manager call with page_name: {page_name}, url_param: {url_params}"
        )
        if not response.is_success():
            raise Exception(
                "Rate Manager API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response

    def get_rate_plan_and_package_data(self, rate_plan_ids):
        logger.info(f"Making rate manager call for rate_plan_ids: {rate_plan_ids}")
        rate_plans_data = self.get_rate_plans_data(rate_plan_ids)['rate_plans']
        for rate_plan_data in rate_plans_data:
            logger.info(
                f"Making rate manager call for package_id: {rate_plan_data['package_id']}"
            )
            package_data = self.get_package_data(rate_plan_data['package_id'])[
                'package'
            ]
            rate_plan_data['package_data'] = package_data
        return rate_plans_data

    def get_rate_plan_and_package_data_by_property(self, property_id):
        rate_plans = self._get_rate_plan_data(property_id).json_response['data'][
            'rate_plans'
        ]
        rate_plan_packages = self._get_package_data(property_id).json_response['data'][
            'packages'
        ]
        package_id_map = {rp_p['package_id']: rp_p for rp_p in rate_plan_packages}
        for rate_plan_data in rate_plans:
            rate_plan_data['package_data'] = package_id_map.get(
                rate_plan_data.get('package_id')
            )
        return rate_plans

    def get_rooms_guardrail(self, data):
        page_name = "get_rooms_guardrail"
        logger.info(
            f"Making rate manager call with page_name: {page_name}, data: {data}"
        )
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Rate Manager API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [
            RoomGuardrailsDTO.create_from_payload(room_guardrails)
            for room_guardrails in response.json_response['data'][
                'room_stay_guardrail_bounds'
            ]
        ]
