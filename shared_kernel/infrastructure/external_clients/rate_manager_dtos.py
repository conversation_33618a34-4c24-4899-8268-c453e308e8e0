class RatePlansDto:
    def __init__(
        self,
        created_at,
        is_active,
        is_flexi,
        name,
        package_name,
        rate_plan_id,
        short_code,
        package_id,
        policies,
        restrictions,
    ):
        self.created_at = created_at
        self.is_active = is_active
        self.is_flexi = is_flexi
        self.name = name
        self.package_name = package_name
        self.rate_plan_id = rate_plan_id
        self.short_code = short_code
        self.package_id = package_id
        self.policies = policies
        self.restrictions = restrictions

    @staticmethod
    def create_from_payload(data):
        rate_plans_data = RatePlansDto(
            created_at=data['created_at'],
            is_active=data['is_active'],
            is_flexi=data['is_flexi'],
            name=data['name'],
            package_name=data['package_name'],
            rate_plan_id=data['rate_plan_id'],
            short_code=data['short_code'],
            package_id=data['package_id'],
            policies=data['policies'],
            restrictions=data['restrictions'],
        )

        return rate_plans_data


class RatePlanInclusionFrequency:
    def __init__(self, count, day_of_serving, frequency_type):
        self.count = count
        self.day_of_serving = day_of_serving
        self.frequency_type = frequency_type


class RatePlanInclusionOffering:
    def __init__(self, quantity, offering_type):
        self.quantity = quantity
        self.offering_type = offering_type


class RatePlanInclusion:
    def __init__(
        self,
        sku_id,
        name,
        frequency: RatePlanInclusionFrequency,
        offering: RatePlanInclusionOffering,
    ):
        self.sku_id = sku_id
        self.name = name
        self.frequency = frequency
        self.offering = offering


class RatePlanPackageDto:
    def __init__(self, package_id, package_name, inclusions: [RatePlanInclusion]):
        self.package_id = package_id
        self.inclusions = inclusions
        self.package_name = package_name

    @staticmethod
    def create_from_payload(package):
        inclusions = [
            RatePlanInclusion(
                sku_id=inclusion['sku_id'],
                name=inclusion['display_name'],
                frequency=RatePlanInclusionFrequency(
                    count=inclusion['frequency']['count'],
                    day_of_serving=inclusion['frequency']['day_of_serving'],
                    frequency_type=inclusion['frequency']['frequency_type'],
                ),
                offering=RatePlanInclusionOffering(
                    quantity=inclusion['offering']['offered_quantity'],
                    offering_type=inclusion['offering']['offering_type'],
                ),
            )
            for inclusion in package['inclusions']
        ]
        return RatePlanPackageDto(
            package_id=package['package_id'],
            inclusions=inclusions,
            package_name=package['package_name'],
        )


class DateWiseBound:
    def __init__(
        self,
        date,
        min_price,
        max_price,
        global_min_price,
        global_max_price,
        applicable_guardrail_ids,
    ):
        self.date = date
        self.min_price = min_price
        self.max_price = max_price
        self.global_min_price = global_min_price
        self.global_max_price = global_max_price
        self.applicable_guardrail_ids = applicable_guardrail_ids

    @staticmethod
    def create_from_payload(payload):
        return DateWiseBound(
            date=payload["date"],
            min_price=payload["guardrail_bounds"]["min_price"],
            max_price=payload["guardrail_bounds"]["max_price"],
            global_min_price=payload["guardrail_bounds"]["global_min_price"],
            global_max_price=payload["guardrail_bounds"]["global_max_price"],
            applicable_guardrail_ids=payload["applicable_guardrails"],
        )


class RoomGuardrailsDTO:
    def __init__(
        self,
        room_stay_id,
        room_type_id,
        adults,
        children,
        date_wise_bounds,
        from_date,
        to_date,
    ):
        self.room_stay_id = room_stay_id
        self.room_type_id = room_type_id
        self.adults = adults
        self.children = children
        self.date_wise_bounds = date_wise_bounds
        self.from_date = from_date
        self.to_date = to_date

    @staticmethod
    def create_from_payload(payload):
        date_wise_bounds = [
            DateWiseBound.create_from_payload(b)
            for b in payload.get("date_wise_bounds", [])
        ]

        return RoomGuardrailsDTO(
            room_stay_id=payload["reference_id"],
            room_type_id=payload["room_type_id"],
            adults=payload["adults"],
            children=payload["children"],
            date_wise_bounds=date_wise_bounds,
            from_date=payload["from_date"],
            to_date=payload["to_date"],
        )
