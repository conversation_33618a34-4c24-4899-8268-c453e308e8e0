from object_registry import register_instance
from shared_kernel.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from shared_kernel.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)


@register_instance()
class TreeboPlatformClient(BaseExternalClient):
    page_map = {
        'generate_hash': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/treebo-platform/hashifier/v1/generate_hash",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_treebo_platform_service_url()

    def generate_hash(self, data_list):
        page_name = "generate_hash"
        post_data = {"data": data_list}
        response = self.make_call(page_name, data=post_data)
        if not response.is_success():
            raise Exception(
                "Treebo Platform generate hash API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def get_hash_value(self, original_data, hash_name, expiry):
        data = [
            {"original_data": original_data, "hash_name": hash_name, "expiry": expiry}
        ]
        json_response = self.generate_hash(data_list=data)
        hashes_data = json_response.get('data', [])
        hash_value = None
        for hash_data in hashes_data:
            if original_data == hash_data['original_data']:
                hash_value = hash_data['hash']

        return hash_value
