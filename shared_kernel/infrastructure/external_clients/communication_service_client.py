from flask import current_app as app
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from object_registry import register_instance
from prometheus import crs_context
from shared_kernel.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from shared_kernel.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)


@register_instance()
class CommunicationServiceClient(BaseExternalClient):
    page_map = {
        'send_communication': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/communication/api/v2/send",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_communication_service_url()

    def get_headers(self):
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)

        if not headers.get('X-Hotel-Id', None):
            headers['X-Hotel-Id'] = (
                crs_context.hotel_context.hotel_id
                if crs_context.hotel_context
                else None
            )

        return headers

    def send_sms_or_whatsapp(
        self, identifier: str, context_data: dict, receivers: list
    ):
        page_name = "send_communication"
        post_date = dict(
            identifier=identifier,
            sms=dict(receivers=receivers),
            whatsapp=dict(receivers=receivers),
            context_data=context_data,
        )
        response = self.make_call(page_name, data=post_date)
        if not response.is_success():
            raise Exception(
                "Communication API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def send_whatsapp(self, identifier: str, context_data: dict, receivers: list):
        page_name = "send_communication"
        post_data = dict(
            identifier=identifier,
            whatsapp=dict(receivers=receivers),
            context_data=context_data,
        )
        response = self.make_call(page_name, data=post_data)
        if not response.is_success():
            raise Exception(
                "Communication API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response

    def send_email(
        self,
        identifier: str,
        context_data: dict,
        to_emails: list,
        subject: str,
        reply_to: str = None,
        from_email: str = None,
        attachments: list = None,
    ):
        page_name = "send_communication"
        post_date = dict(
            identifier=identifier,
            context_data=context_data,
            email=dict(
                to_emails=to_emails,
                reply_to=reply_to,
                from_email=from_email,
                subject=subject,
                attachments=attachments if attachments else [],
            ),
        )

        response = self.make_call(page_name, data=post_date)
        if not response.is_success():
            raise Exception(
                "Communication API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response
