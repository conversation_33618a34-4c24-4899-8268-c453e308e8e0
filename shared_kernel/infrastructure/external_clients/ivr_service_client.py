import os
from datetime import timedelta

import requests
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.utils.dateutils import current_datetime, local_timezone


class IVRServiceClient(object):
    @classmethod
    def send_ivr(
        cls,
        data: dict,
        tenant_id: str,
    ):
        ivr_details = cls.get_ivr_secrets(tenant_id)

        ivr_obd_key = ivr_details["ivr_obd_key"]
        ivr_obd_authorization = ivr_details["ivr_obd_authorization"]
        ivr_obd_host = ivr_details["ivr_obd_host"]
        ivr_api_url = ivr_details["ivr_api_url"]

        ivr_headers = {
            "x-api-key": ivr_obd_key,
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": ivr_obd_authorization,
        }

        url = f"{ivr_obd_host}/{ivr_api_url}"
        response = requests.post(url, json=data, headers=ivr_headers)

        if response.status_code == requests.codes.ok:
            return response.text

        raise Exception(
            "IVR API Error. Status Code: {0}, Errors: {1}".format(
                response.status_code, response.text
            )
        )

    @staticmethod
    def create_ivr_payload(phone, hotel_id, flow_settings, settings_service):
        ivr_generic_settings = settings_service.get_knowlarity_base_settings(hotel_id)
        payload = {
            "additional_number": phone,
            "timezone": local_timezone().zone,
            "start_time": (current_datetime() + timedelta(minutes=2)).strftime(
                "%Y-%m-%d %H:%M"
            ),
            "end_time": (current_datetime() + timedelta(minutes=19)).strftime(
                "%Y-%m-%d %H:%M"
            ),
        }
        payload.update(ivr_generic_settings)
        payload.update(flow_settings)
        return payload

    @staticmethod
    def get_ivr_secrets(tenant_id):
        if os.environ.get('APP_ENV', 'local') == "local":
            return {
                "ivr_obd_key": os.getenv("IVR_OBD_KEY"),
                "ivr_obd_authorization": os.getenv("IVR_OBD_AUTHORIZATION"),
                "ivr_obd_host": os.getenv("IVR_OBD_HOST"),
                "ivr_api_url": os.getenv("IVR_API_URL"),
            }

        return AwsSecretManager.get_secret(tenant_id, "ivr_details")
