from treebo_commons.utils import dateutils

from shared_kernel.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from shared_kernel.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)


class CRSClient(BaseExternalClient):
    page_map = {
        'get_room_type_inventories': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/v1/hotels/{hotel_id}/room-type-inventories?from_date={from_date}&"
            "to_date={to_date}&room_type_id={room_type_id}",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_crs_service_url()

    def get_room_type_inventories(
        self, hotel_id, checkin, checkout, crs_room_type_id=None
    ):
        page_name = "get_room_type_inventories"
        url_params = dict(
            hotel_id=hotel_id,
            from_date=checkin,
            to_date=dateutils.subtract(checkout, days=1),
            room_type_id=crs_room_type_id,
        )
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "CRS API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response['data']
