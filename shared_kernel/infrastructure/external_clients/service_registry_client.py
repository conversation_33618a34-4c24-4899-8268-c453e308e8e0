import enum
import logging
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

logger = logging.getLogger(__name__)


class ServiceEndPointNames(enum.Enum):
    CATALOG_SERVICE_URL = "catalog_service_url"
    TENANT_SERVICE_URL = "tenant_service_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"
    IVR_SERVICE_URL = "ivr_service_url"
    CRS_SERVICE_URL = "crs_service_url"
    RATE_MANAGER_SERVICE_URL = "rate_manager_service_url"
    TREEBO_PLATFORM_URL = "treebo_platform_service_url"


class ServiceRegistryClient:
    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tenant_service_url(cls):
        service_name = ServiceEndPointNames.TENANT_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_ivr_service_url(cls):
        service_name = ServiceEndPointNames.IVR_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_crs_service_url(cls):
        service_name = ServiceEndPointNames.CRS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_rate_manager_service_url(cls):
        service_name = ServiceEndPointNames.RATE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_treebo_platform_service_url(cls):
        service_name = ServiceEndPointNames.TREEBO_PLATFORM_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
