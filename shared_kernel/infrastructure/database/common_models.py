from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Date, DateTime, Integer, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin


class SellerModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "seller"

    seller_id = Column(String, primary_key=True)
    hotel_id = Column(String)
    seller_category_id = Column(Integer)
    name = Column(String)

    state_id = Column(Integer)
    state_name = Column(String)
    city_id = Column(Integer)
    city_name = Column(String)
    pincode = Column(String)

    # GST Details
    legal_name = Column(String)
    legal_address = Column(String)
    gstin_num = Column(String)
    fssai_license_number = Column(String)
    legal_signature = Column(String)
    legal_city_id = Column(Integer)
    legal_city_name = Column(String)
    legal_state_id = Column(Integer)
    legal_state_name = Column(String)
    legal_pincode = Column(String)

    status = Column(String)
    base_currency = Column(String)
    timezone = Column(String)

    seller_config = Column(JSON)
    seller_type = Column(String)
    current_business_date = Column(Date)


class SellerTableModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = 'seller_table'

    table_id = Column(String, primary_key=True)
    seller_id = Column(String, nullable=False)
    name = Column(String, nullable=True)
    table_number = Column(String, nullable=True)
    current_status = Column(String, nullable=True)
    status_updated_at = Column(DateTime(timezone=True))
