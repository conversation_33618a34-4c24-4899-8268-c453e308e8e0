from ths_common.value_objects import Address, GSTDetails, PhoneNumber


class SellerDetails(object):
    def __init__(
        self,
        seller_id,
        name,
        state_code,
        gst_details,
        phone=None,
        email=None,
        url=None,
        address=None,
        legal_signature=None,
        fssai_license_number=None,
    ):
        self.seller_id = seller_id
        self.name = name
        self.state_code = state_code
        self.gst_details = gst_details
        self.phone = phone
        self.email = email
        self.url = url
        self.address = address
        self.legal_signature = legal_signature
        self.fssai_license_number = fssai_license_number

    def to_json(self):
        # NOTE: Using vendor_name and vendor_id below, as this json will be sent to Bill
        # In Billing Service, we treat - hotel and seller using 1 common term - "vendor"
        return {
            "vendor_name": self.name,
            "vendor_id": self.seller_id,
            "state_code": self.state_code,
            "gst_details": self.gst_details.to_json() if self.gst_details else None,
            "phone": self.phone.to_json() if self.phone else None,
            "email": self.email,
            "url": self.url,
            "address": self.address.to_json() if self.address else None,
            "legal_signature": self.legal_signature,
            "fssai_license_number": self.fssai_license_number,
        }

    @staticmethod
    def from_json(json):
        return SellerDetails(
            seller_id=json.get('vendor_id'),
            name=json.get('vendor_name'),
            state_code=json.get('state_code'),
            gst_details=GSTDetails.from_json(json.get('gst_details'))
            if json.get('gst_details')
            else None,
            phone=PhoneNumber.from_json(json.get('phone'))
            if json.get('phone')
            else None,
            email=json.get('email'),
            url=json.get('url'),
            address=Address.from_json(json.get('address'))
            if json.get('address')
            else None,
            legal_signature=json.get('legal_signature'),
            fssai_license_number=json.get('fssai_license_number'),
        )
