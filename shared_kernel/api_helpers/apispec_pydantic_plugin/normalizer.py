from copy import deepcopy
from typing import Any, Dict


class APISpecSchemaNormalizer:
    @staticmethod
    def normalize_pydantic_schema(schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize a Pydantic-generated JSON schema to:
        - Convert 'anyOf: [type, null]' into 'type' with 'nullable: true'
        - Remove default: None
        """

        normalized_schema = deepcopy(schema)

        # Normalize top-level properties
        APISpecSchemaNormalizer._normalize_properties(
            normalized_schema.get("properties", {})
        )

        # Normalize nested definitions
        for sub_schema in normalized_schema.get("$defs", {}).values():
            APISpecSchemaNormalizer._normalize_properties(
                sub_schema.get("properties", {})
            )

        return normalized_schema

    @staticmethod
    def _normalize_properties(properties: Dict[str, Any]) -> None:
        for prop_name, prop_schema in properties.items():
            APISpecSchemaNormalizer._normalize_nullable_field(prop_schema)

    @staticmethod
    def _normalize_nullable_field(field_schema: Dict[str, Any]) -> None:
        any_of = field_schema.get("anyOf")
        if not isinstance(any_of, list) or len(any_of) != 2:
            return

        has_null = any(t.get("type") == "null" for t in any_of)
        non_null = next((t for t in any_of if t.get("type") != "null"), None)

        if has_null and non_null:
            field_schema.clear()
            field_schema.update(non_null)
            field_schema["nullable"] = True
            if field_schema.get("default") is None:
                field_schema.pop("default", None)
