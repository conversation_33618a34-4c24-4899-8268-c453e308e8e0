from __future__ import annotations

from typing import TYPE_CHECKING, ClassVar, Type

from shared_kernel.api_helpers.apispec_pydantic_plugin.errors import ModelNotFoundError

if TYPE_CHECKING:
    from shared_kernel.api_helpers.apispec_pydantic_plugin import BaseModelAlias


class Registry:
    # we're using BaseModel instead of ApiBaseModel so that users may manually
    # register classes with the registry, if they choose.
    registered: ClassVar[dict[str, Type[BaseModelAlias]]] = {}

    @classmethod
    def register(cls, model: Type[BaseModelAlias]) -> None:
        name = model.__name__
        if name in cls.registered:
            raise ValueError(f"Duplicate schema received by registry: {name}")
        cls.registered[name] = model

    @classmethod
    def get(cls, name: str) -> Type[BaseModelAlias]:
        try:
            return cls.registered[name]
        except KeyError as e:
            raise ModelNotFoundError(f"Model not found: {name}") from e
