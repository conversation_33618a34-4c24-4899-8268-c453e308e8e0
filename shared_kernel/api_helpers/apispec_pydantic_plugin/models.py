from typing import Any, TypeVar, Union

from pydantic import BaseModel, RootModel

from shared_kernel.api_helpers.apispec_pydantic_plugin.registry import Registry

_T = TypeVar("_T")


class ApiBaseModel(BaseModel):
    def __init_subclass__(cls, **kwargs: Any) -> None:
        Registry.register(cls)
        return super().__init_subclass__(**kwargs)

    @classmethod
    def dump(cls, instance, many=False, **kwargs):
        if many:
            return [cls._dump_instance(ins, **kwargs) for ins in instance]
        return cls._dump_instance(instance, **kwargs)

    @classmethod
    def _dump_instance(cls, instance, **kwargs):
        if isinstance(instance, cls):
            return instance.model_dump(**kwargs, mode='json')
        return cls.model_validate(instance).model_dump(**kwargs, mode='json')


# pylint: disable=inherit-non-class
class ApiRootModel(RootModel[_T]):
    def __init_subclass__(cls, **kwargs: Any) -> None:
        Registry.register(cls)
        return super().__init_subclass__(**kwargs)


BaseModelAlias = Union[ApiBaseModel, ApiRootModel[Any], BaseModel]
