from typing import Any, Optional

from apispec.ext.marshmallow import MarshmallowPlugin
from pydantic import BaseModel

from shared_kernel.api_helpers.apispec_pydantic_plugin import PydanticPlugin


class GracefulMarshmallowAPISpecPlugin(MarshmallowPlugin):
    def schema_helper(self, name, _, schema=None, **kwargs):
        try:
            return super().schema_helper(name, _, schema=schema, **kwargs)
        except ValueError:
            return None


class GracefulPydanticAPISpecPlugin(PydanticPlugin):
    def schema_helper(
        self,
        name: str,  # noqa: ARG002
        definition: dict[Any, Any],  # noqa: ARG002
        schema=None,
        **kwargs: Any,
    ) -> Optional[dict[str, Any]]:
        if (isinstance(schema, type) and issubclass(schema, BaseModel)) or isinstance(
            schema, BaseModel
        ):
            return super().schema_helper(name, definition, schema=schema, **kwargs)

        return None
