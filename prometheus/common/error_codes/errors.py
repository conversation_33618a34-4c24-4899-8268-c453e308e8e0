from ths_common.exceptions import CRSError

# Application Layer error codes starts from 50 and each application layer module will have its own module level code
# for example 50XX, 51XX..., 72XX, ...


class ApplicationErrors(CRSError):
    # Booking Application service (50XX)
    BOOKING_STATE_INVALID_FOR_REPLACEMENT = (
        "5001",
        "Booking should be in reserved/confirmed state to allow replacement",
    )
    CANT_ADD_EXPENSE_IF_NOT_ASSIGNED = (
        "5002",
        "Charge cannot be added if not assigned to a guest.",
    )
    CANT_ADD_FUTURE_EXPENSE = "5003", "Addition of future charge is not allowed."
    INVALID_BOOKING_FOR_EDIT = (
        "5004",
        "Cannot update booking. Booking is either cancelled or marked noshow",
    )
    INVALID_ROOM_FOR_ADD_GUEST = (
        "5005",
        "Cannot add guest stay. RoomStay is either cancelled or marked noshow",
    )
    DUPLICATE_GUESTS = (
        "5006",
        "Expense is being assigned to the same guest twice. Please check payload",
    )
    INVALID_ROOM_FOR_EDIT = (
        "5007",
        "Cannot update room stay. RoomStay is either cancelled or marked noshow",
    )
    CREATION_TIME_GREATER_THAN_TODAY = (
        "5008",
        "Booking creation time should not be greater than today",
    )
    # CANT_CREATE_TEMP_BOOKING_WITHOUT_HOLD_TILL = "5009", "Cannot create a temp booking without a hold till time"
    CANT_CREATE_CONFIRMED_BOOKING_WITH_HOLD_TILL = (
        "5010",
        "Cannot create a confirmed booking with a hold till time",
    )
    GUEST_ALLOCATION_UPDATE_ERROR = (
        "5011",
        "Cannot update guest allocation on non-reserved guest stay",
    )
    FUTURE_DATE_STATISTICS_NOT_ALLOWED = (
        "5012",
        "Cannot fetch future business date house statistics",
    )
    # Billing Application service (51XX)
    BILL_VERSION_MANDATORY = (
        "5101",
        "Bill version is mandatory for update operations. Please contact escalations.",
    )
    CHARGE_CANCELLATION_STATE_CHANGE_ONLY_ALLOWED = (
        "5102",
        "This operation is not allowed for this charge.",
    )
    CHARGE_ALREADY_INVOICED = (
        "5103",
        "Some charges already invoiced. Please select only those charges which are not yet invoiced.",
    )
    EMPTY_CREDIT_NOTE_GENERATION = (
        "5104",
        "Cannot generate credit note without any line items",
    )
    ADD_CHARGE_NOT_ALLOWED_FOR_CRS_VIA_API = (
        "5105",
        "Add charge not allowed for crs via api",
    )
    CANT_MODIFY_POSTED_PAYMENT = "5106", "Payment in posted state cannot be modified"

    # Booking Invoicing service (52XX)
    BILL_TO_TYPE_NOT_SUPPORTED = (
        "5201",
        "Bill to derivation only supported for BillToType: Company, Guest",
    )
    FULL_PREVIEW_NOT_ALLOWED_PART_CHECKIN = (
        "5202",
        "Cannot preview invoice for all guests for partially checked-in room.",
    )
    NO_INVOICE_TO_REGENERATE = "5203", "There is no invoice to be regenerated."
    INVOICE_DOES_NOT_BELONG_TO_BILL = "5204", "Invoice does not belong to bill"
    INVALID_INVOICE_STATE_FOR_REGENERATION = (
        "5205",
        "Invoice should be in generated or regenerated state to regenerate it.",
    )
    INVOICE_VERSION_MANDATORY_FOR_UPDATE = (
        "5206",
        "Invoice version is mandatory for update operations. Please contact escalations.",
    )
    INVOICE_BILL_TO_CANNOT_CHANGE_FOR_BILL_TO_TYPE_COMPANY = (
        "5207",
        "Change of Bill-To not allowed.",
    )
    NO_GUEST_SELECTED_FOR_INVOICE = "5208", "No guests selected."
    GUEST_INVALID_STATE_FOR_INVOICE_PREVIEW = (
        "5209",
        "Selected guests should be in checked-in state",
    )
    INVOICE_BILL_TO_CANNOT_BE_NULL = "5210", "Bill-To is mandatory for invoice edit"
    PREVIEW_CANNOT_BE_CALLED_BEFORE_CHECKIN = (
        "5211",
        "Cannot preview invoice before booking checkin.",
    )
    CANT_BILL_BTG_INVOICE_TO_COMPANY = (
        "5212",
        "Can't set Bill-To to Corporate for BTG invoice.",
    )

    # Booking charge consumption service (53XX)
    NO_CHECKED_IN_USER_FOR_AUTO_CHARGE_SPLIT = (
        "5301",
        "No checked in users found for auto charge split.",
    )

    # Booking Lifecycle Application (54XX)
    INVALID_INVOICE_GROUP = "5401", "Invoice group does not belong to given booking id."
    INVOICE_GROUP_NOT_IN_PREVIEW_STATE = '5402', "Invoice group not in preview state."
    INVOICE_DOES_NOT_BELONG_TO_BOOKING_FOR_CHECKOUT = (
        "5403",
        "Invoice does not belong to the booking.",
    )
    INVOICE_NOT_IN_PREVIEW_STATE_FOR_CHECKOUT = (
        "5404",
        "Invoice should be in preview state for checkout.",
    )
    INVOICE_GENERATION_MANDATORY_FOR_ROOM_CHECKOUT = (
        "5405",
        "Invoice generation cannot be skipped for complete room checkout",
    )
    PAYMENT_PENDING_FOR_CHECKOUT = "5406", "Payment pending for checkout."
    ACTION_DOES_NOT_BELONG_TO_BOOKING = (
        "5407",
        "The action does not belong to the given booking.",
    )
    ACTION_CANNOT_BE_CANCELLED = "5408", "The actions provided cannot be reversed."
    ONLY_LATEST_ACTION_CAN_BE_CANCELED = "5409", "Only latest action can be cancelled."
    NO_ACTIVE_ACTION_FOUND = "5410", "No active actions present in booking."
    BTC_INVOICE_WITHOUT_COMPANY_DETAIL = (
        "5411",
        "Can't generate BTC-Credit invoice without company details. "
        "Please provide company legal name and corporate code in GST Details",
    )
    CANNOT_REVERSE_PAST_DATED_ACTION = (
        "5412",
        "Hotel's current business date is greater than the {action}'s business date, Hence this reversal cannot be "
        "performed.",
    )

    # Catalogue service (55XX)
    ROOM_TYPE_CONFIG_NOT_FOUND = "5501", "Room type doesn't exists in this hotel"
    ROOM_TYPE_CONFIG_ALREADY_EXISTS = "5502", "Room type already exists"
    ROOM_ALREADY_EXISTS = "5503", "Room already exists"

    ASSIGNED_HOUSEKEEPER_DELETION = (
        "5520",
        "Can't delete housekeeper, which is assigned to a room.",
    )
    HOUSEKEEPING_STATUS_UPDATED_DISALLOWED_FROM_DNR = (
        "5521",
        "Housekeeping status updated disallowed till the room is Out Of Order",
    )

    # Hotel config exceptions (56XX)
    HOTEL_NOT_MANAGED_BY_CRS = (
        "5601",
        "Hotel is not active on Treebo PMS. Please contact Escalations",
    )
    MIGRATION_ALREADY_COMPLETED = (
        "5602",
        "Migration is already completed. Can't re-initiate migration.",
    )
    MIGRATION_ALREADY_IN_PROGRESS = (
        "5603",
        "Migration is already in progress. Can't run Dry Run.",
    )
    ROLLBACK_NOT_ALLOWED = (
        "5604",
        "Rollback Migration is not allowed. Hotel was made live before yesterday.",
    )

    # Inventory Application service (57XX)
    INVENTORY_NOT_CREATED = (
        "5701",
        "Room availability for the given dates is unknown. Please contact escalations.",
    )
    ROOM_INVENTORY_NOT_CREATED = (
        "5702",
        "Room type availability for the given dates is unknown. Please contact "
        "escalations.",
    )
    INVENTORY_AUTO_UPDATE_FAILED = "5703", "Inventory auto room assignment failed."
    CANT_EDIT_INACTIVE_DNR = "5704", "Inactive DNRs can't be edited"
    CANT_MARK_PAST_DATES_AS_DNR = "5705", "Out Of Order cannot be marked for past dates"
    CANT_DELETE_PAST_DATED_DNR = (
        "5706",
        "Out Of Order starting in past date cannot be deleted. Please resolve it "
        "instead",
    )
    CANT_EDIT_START_DATE_OF_DNR_STARTING_IN_PAST = (
        "5707",
        "Start date for past dated Out of Order cannot be changed",
    )
    CANT_EXTEND_RESOLVED_DNR = (
        "5708",
        "Already resolved Out of Order cannot be extended. Please mark the room Out "
        "of Order again",
    )
    CANT_MOVE_DNR_END_DATE_TO_PAST_DATE = (
        "5709",
        "Out Of Order end date cannot be moved to current or past date. "
        "Please use Resolve DNR to clear the DNR starting today",
    )
    CANT_EDIT_END_DATE_OF_INACTIVE_DNR = (
        "5710",
        "End date for an inactive DNR can't be edited",
    )

    # Night Audit service (58XX)
    NIGHT_AUDIT_FAILURE = "5801", "Night audit failed"
    BUSINESS_DATE_ROLLOVER_TO_FUTURE_NOT_ALLOWED = (
        "5802",
        "Can't rollover business date greater than current calendar date",
    )

    # Integration Event
    INTEGRATION_EVENT_DTO_FAILURE_NO_DATA_PASSED = (
        "5900",
        "Required data not passed for generating integration event",
    )
    INTEGRATION_EVENT_DTO_HOTEL_ID_MISMATCH = (
        "5901",
        "Hotel Id doesn't match between entities sent in the event",
    )
    CHARGE_EDIT_CAN_NOT_BE_MIXED_WITH_CHARGE_CANCEL = (
        "5902",
        "Charge edit can not be mixed with the charge cancellations",
    )
    INVOICE_LOCK_STATUS_UPDATE_FAILURE = (
        '5903',
        "Invoice lock and gst status update failed",
    )

    # hotel side invoice generation flow
    INVOICE_NOT_ON_RESELLER = "5904", "Invoice requested is not on reseller."
    HOTEL_INVOICE_ALREADY_EXISTS = (
        "5905",
        "Hotel side invoice for the provided invoice already exists",
    )
    INVOICE_NOT_CONFIRMED_YET = (
        "5907",
        "Invoice is still in preview state, please confirm the invoice",
    )

    # hotel side credit note generation flow
    CREDIT_NOTE_IN_CANCELLED_STATE = "5908", "Credit note has been cancelled"
    CREDIT_NOTE_NOT_ON_RESELLER = "5909", "Credit note is not issued by reseller"
    HOTEL_CREDIT_NOTE_ALREADY_EXISTS = (
        "5910",
        "Hotel side credit note for the provided invoice already exists",
    )

    # sequence number block for invoice
    RESELLER_INVOICE_SERIES_BLOCK = (
        "5911",
        "block_hotel_invoice_sequence field is mandatory for issued by reseller",
    )

    # sequence number block for credit note or
    RESELLER_CREDIT_NOTE_SERIES_BLOCK = (
        "5912",
        "block_hotel_credit_note_sequence field is mandatory for issued by reseller",
    )

    INVALID_GSTIN_RESET_REQUEST = (
        "5913",
        "GSTIN received doesn't match the GSTIN of hotel. "
        "GSTIN set in catalog: {gstin}",
    )

    CANCEL_NO_SHOW_EXPENSE_TO_BOOKING_OWNER = (
        "5914",
        "The cancellation no-show expense should be " "assigned to booking owner",
    )

    CANNOT_ADD_CANCEL_EXPENSE_FOR_ROOM_STAY_NOT_CANCELLED = (
        "5915",
        "The cancellation charge cannot be added for a "
        "roomstay which is not in cancelled state",
    )
    CANNOT_ADD_NO_SHOW_EXPENSE_FOR_ROOM_STAY_NOT_NO_SHOW = (
        "5916",
        "The no_show charge cannot be added for a "
        "roomstay which is not in no show state",
    )

    WORKING_ON_OLDER_VERSION_OF_INVOICE_GROUP = (
        "5917",
        "Working on the older version of invoice group, one or more "
        "invoice status have been updated, please retry",
    )

    INCLUDE_CANCELLATION_CHARGE_CANNOT_BE_DISABLED = (
        "5918",
        "Include cancellation charge cannot be disabled for the "
        "last checkout of the booking",
    )

    ROOM_DOES_NOT_EXIST = "5919", "Room does not exist to update"

    BOOKINGS_NOT_AVAILABLE_TO_MARK_OVERFLOW = (
        "5920",
        "Bookings not available to mark overflow",
    )
    ROOM_STAY_OVERLAPPING_IS_MANDATORY = (
        "5921",
        "Room stays to be marked overflow, should overlap with the replacement",
    )
    BULK_OVERFLOW_UNMARK_NOT_ALLOWED = (
        "5922",
        "Bulk unmarking of room stay overflows is not allowed",
    )
    MARKING_OVERFLOW_NOT_ALLOWED_ON_CHANNEL = (
        "5923",
        "Marking overflow is allowed only on DIRECT and OTA channels",
    )
    OVERFLOW_UNMARKING_CREATED_OVERFLOW_ANOMALY = (
        "5924",
        "Unmarking an overflow created overflow-inventory mismatch",
    )

    CHARGE_ID_NOT_IN_ROOM_STAY = (
        "5925",
        "Charge ID doesn't belong to the selected room stay",
    )
    CANT_HAVE_BOTH_PRETAX_AND_POSTTAX = (
        "5926",
        "Input can contain only one of pre-tax or post-tax amount.",
    )
    GUEST_NOT_STAYED_ON_GIVEN_DATE = (
        "5927",
        "The selected guest has not stayed in the room on selected date",
    )
    CUSTOMER_NOT_FOUND = "5928", "Customer not found"

    HOTEL_ID_MISMATCH_IN_REQUEST = (
        "5929",
        "Hotel Id doesn't match between entities in the request",
    )
    ROOM_TYPE_ID_MISMATCH_IN_REQUEST = (
        "5930",
        "Room Type Id doesn't match between entities in the request",
    )
    ROOM_STAY_OVERFLOW_MARK_FAILED = (
        "5931",
        "Cannot mark room stay overflow, while in overflow state",
    )
    ROOM_STAY_OVERFLOW_UNMARK_FAILED = (
        "5932",
        "Cannot unmark room stay overflow, while not in overflow state",
    )
    ROOM_STAY_MUST_BE_IN_RESERVED_STATE = (
        "5933",
        "Room Stays must be in reserved state for swap operation",
    )
    PAYMENT_PENDING_FOR_INVOICE_GENERATION = (
        "5934",
        "Payments should be added to cover all the "
        "non credit charges that are invoiced",
    )
    REVERSE_CHECKOUT_NOT_ALLOWED_AFTER_NEW_INVOICED_CHARGE_ADDITION_TO_ROOM_POST_CHECKOUT = (
        "5935",
        "Reversal of "
        "checkout not "
        "allowed after "
        "addition of new "
        "invoice charges "
        "to room post "
        "checkout",
    )
    CASHIER_SESSION_ALREADY_OPEN = (
        "5936",
        "New cashier session cannot be created, Cashier Session already open",
    )
    NO_CASH_REGISTER_FOUND = "5937", "No cash register found"
    NO_CASHIER_SESSION_FOUND = "5938", "No cashier session found"
    WEB_CHECKIN_NOT_COMPLETE = (
        "5939",
        "Web checkin can be marked complete only when guest attachments are verified "
        "and all rooms are assigned",
    )
    HOTEL_NOT_FOUND = "5940", "Hotel not found"
    CHARGE_ITEM_HSN_CODE_NOT_FOUND = (
        "5941",
        "HSN Code/Category for charged item not found. Can't invoice the charge",
    )
    SKU_NOT_FOUND = "5942", "Sku not found"
    ADD_ALLOWANCE_NOT_ALLOWED_FOR_POS = "5943", "Add allowance not allowed for pos"
    TRANSFER_CHARGE_NOT_ALLOWED_FOR_POS = "5944", "Transfer charge not allowed for pos"
    CHARGE_TRANSFER_ONLY_ALLOWED_TO_CONFIRMED_OR_CHECKEDIN_BOOKING = (
        "5945",
        "Transfer of charge is only allowed to " "confirmed or checked-in booking",
    )
    BILLED_ENTITY_CANNOT_BE_EMPTY = (
        "5946, Billed Entity Account Cannot be empty for CRS App"
    )
    CANNOT_GENERATE_CREDIT_NOTE_ON_MULTIPLE_INVOICES = (
        "5947",
        "Cannot generate credit note on multiple invoices",
    )
    CHARGE_TRANSFER_ONLY_ALLOWED_TO_BOOKINGS_OF_SAME_HOTEL = (
        "5948",
        "Transfer of charge is only allowed to booking " "of same hotel",
    )
    INVALID_RATE_PLAN_ID = "5949", "Invalid rate plan id"

    UPDATE_ALLOWANCE_NOT_ALLOWED_FOR_POS = (
        "5950",
        "Update allowance not allowed for pos",
    )
    RATE_PLAN_ADDON_UPDATE_API_NOT_SUPPORTED = (
        "5951",
        "Addon update API for Rate Plan Addon is not supported",
    )
    LINKED_ADDON_UPDATE_API_NOT_SUPPORTED = (
        "5952",
        "Addon update API for Room Linked Addon is not supported",
    )
    RATE_PLAN_ADDON_DELETE_API_NOT_SUPPORTED = (
        "5953",
        "Addon delete API for Rate Plan Addon is not supported",
    )
    LINKED_ADDON_DELETE_API_NOT_SUPPORTED = (
        "5954",
        "Addon delete API for Room Linked Addon is not supported",
    )
    INVOICE_ACCOUNT_AVAILABLE_ON_CHECKOUT = (
        "5955",
        "This account can only be invoiced on checkout",
    )
    CANCEL_OR_MOVE_BOOKED_CHARGES_TO_OTHER_ACCOUNT = (
        "5956",
        "Cancel or move booked future charges to some other "
        "account to invoice this account",
    )
    CASHIER_SESSION_NOT_OPEN = "5957", "Cashier Session not open"
    CASHIER_SESSION_ID_INVALID = "5958", "Cashier Session ID is invalid"
    CREDIT_NOT_ALREADY_GENERATED = (
        "5959",
        "Credit Note already generated for the invoice charges",
    )
    CANT_ADD_REFUND_VIA_CREDIT_SHELL = (
        "5960",
        "Can't add refund via credit shell . Restricted to Payment only",
    )

    DUPLICATE_INVOICE_CHARGE_ID = "5961", "Duplicate Invoice charge id in request"

    INVOICE_CHARGES_MISMATCH_FOR_INVOICE = (
        "5962",
        "Invoice charges mismatch for the invoice",
    )
    INVALID_STATUS_REQUEST_FOR_ALLOWANCE = (
        "5963",
        "Allowance status update only allowed to consumed/cancelled",
    )
    CHARGE_EDIT_CAN_NOT_BE_MIXED_WITH_CHARGE_CONSUME = (
        "5964",
        "Charge edit can not be mixed with the charge " "consumptions",
    )
    CANT_UPDATE_RATE_PLAN_AS_RATE_MANAGER_IS_DISABLED = (
        "5965",
        "Can't update rate plan as rate manager " "is disabled for the property",
    )
    INVALID_ROOM_FOR_RATE_PLAN_EDIT = (
        "5966",
        "Cannot update room stay rate plan. Only RoomStays in reserved"
        " state allows rate plan update",
    )
    RATE_PLAN_REFERENCE_ID_MISSING = (
        "5967",
        "Cannot update rate plan. Rate plan reference id not provided",
    )
    RATE_PLAN_INCLUSION_PROVIDED_WITHOUT_RATE_PLAN_REF = (
        "5968",
        "Cannot add Rate Plan Inclusions. Rate Plan "
        "Inclusions provided without Rate plan reference id",
    )
    RATE_PLAN_INCLUSION_OUT_OF_STAY_DURATION = (
        "5969",
        "Cannot add Rate Plan Inclusions. Rate Plan Inclusions "
        "provided are out of stay duration",
    )
    CHARGE_TRANSFER_NOT_ALLOWED_WHEN_APPLICABLE_DATE_NOT_IN_STAY_DATE_RANGE = (
        "5970",
        "This charge cannot be transferred as "
        "the charge's applicable date is not "
        "within the date range of destination "
        "booking.",
    )
    PAYMENT_PENDING_MARK_NO_SHOW_OR_CANCELLATION = (
        "5971",
        "There are outstanding charges to be paid for this "
        "booking. Please record payment against these charges,"
        " and then proceed",
    )
    REISSUE_NOT_ALLOWED = (
        "5972",
        "Cannot reissue this invoice as this includes charges which has splits and slabbed taxation",
    )

    EDIT_CHARGE_AMOUNT_NOT_ALLOWED_FOR_TAX_SLAB_BASED_CHARGE_SPLITS = (
        "5973",
        "Cannot edit this charge as this has splits and slabbed taxation.",
    )

    NON_FINANCIAL_REISSUE_FLOW_IS_NOT_ALLOWED_FOR_GIVEN_ISSUED_TYPES = (
        "5974",
        "Non-financial reissue (modification) is only allowed for reseller to customer invoice",
    )

    CANNOT_SPLIT_INVOICE_IN_NON_FINANCIAL_REISSUE = (
        "5975",
        "Cannot split invoice in Non-financial reissue (modification)",
    )

    CANNOT_MERGE_INVOICE_IN_NON_FINANCIAL_REISSUE = (
        "5976",
        "Cannot merge invoice in Non-financial reissue (modification)",
    )

    RATE_PLAN_NOT_AVAILABLE_IN_DESTINATION_HOTEL = (
        "5977",
        "Cannot migrate booking. Corresponding rate plan not available in destination hotel.",
    )

    NON_FINANCIAL_REISSUE_FLOW_IS_NOT_ALLOWED_DUE_TO_TAX_CONFIG_CHANGES = (
        "5978",
        "Non-financial reissue (modification) is only allowed. Tax determiners got updated in booking",
    )

    INVALID_PAYOR_FOR_PAYMENT_METHOD = (
        "5979",
        "Invalid payor for the given payment method",
    )

    INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS = (
        "5980",
        "Cannot update tax determiners as booking has non updatable charges",
    )

    INVALID_PAYMENT_METHOD = ("5981", "Invalid payment method")

    RECORD_EXTRA_PAYMENT_COMPARED_TO_BOOKING_BALANCE = (
        "5982",
        "You are trying to record extra payment compared to booking balance.",
    )

    REFUND_AMOUNT_GREATER_THAN_RECEIVED_PAH_PAYMENT = (
        "5983",
        "You are trying to issue extra refund, via hotel, compared to payment received.",
    )

    REFUND_AMOUNT_GREATER_THAN_RECEIVED_PTT_PAYMENT = (
        "5984",
        "You are trying to issue extra refund, compared to payment received.",
    )

    NO_CASHIER_SESSION_FOUND_ON_HOTEL = (
        "5985",
        "Cashier session is not found for the hotel",
    )

    CHARGE_CANNOT_BE_SPLIT_TO_DIFFERENT_GST_CONFIG = (
        "5986",
        "Charge cannot be split to billed entities having different tax configs",
    )
    UN_SUPPORTED_ACTION_FOR_TENANT = (
        "5987",
        "This particular action is unavailable for this tenant",
    )
    INCORRECT_PAYMENT_MODE_FOR_CREDIT_SHELL_REFUND = (
        "5988",
        "Incorect payment mode for credit shell refund",
    )
    CANCELLATION_CHARGE_MISMATCH = ("5989", "Cancellation charge mismatch")
    REFUND_FAIlURE = ("5990", "Failed to add refund, please contact escalation team")
    CANCELLATION_CHARGE_REQUIRED_FOR_CUSTOM_CANCELLATION_FEE = (
        "5991",
        "Cancellation charge required for custom cancellation fee",
    )
    CANCELLATION_CHARGE_GREATER_THAN_BOOKING_AMOUNT = (
        "5992",
        "Cancellation charge greater than booking amount",
    )
    CONTACT_DETAILS_REQUIRED_FOR_GENERATING_PAYOUT_LINK = (
        "5993",
        "contact details required for generating payout link",
    )
    CANNOT_CANCEL_POSTED_REFUND = ("5994", "Posted refund cannot be cancelled")
    INVALID_CANCELLATION_POLICY = ("5995", "Invalid Cancellation Policy")
    BOOKING_RELOCATION_NOT_ALLOWED = ("5996", "Booking relocation is not allowed")
    AUTO_REFUND_NOT_POSSIBLE = ("5997", "Auto Refund Not Possible")
    BOOKING_RELOCATION_NOT_POSSIBLE_ON_SAME_BOOKING = (
        "5998",
        "Booking relocation not possible on same booking",
    )
    PAYMENT_ALREADY_EXISTS_WITH_REFERENCE_NUMBER = (
        "5999",
        "Payment already exists with reference number",
    )
    CANNOT_ADD_EXPENSE_TO_THIS_ROOM_STAY = (
        "6000",
        "Add expense disallowed to this room-stay",
    )
    CANNOT_DO_MANUAL_FUNDING_ON_CANCELLED_OR_NOSHOW_BOOKING = (
        "6001",
        "Cannot do manual funding on cancelled or noshow booking",
    )
    FUNDING_AMOUNT_CANNOT_BE_GREATER_THAN_DELTA = (
        "6002",
        "Manual funding amount cannot be greater than delta: ({amount})",
    )
    BOOKING_FUNDING_NOT_ENABLED = ("6003", "Booking funding not enabled for this hotel")
    FUNDING_REQUEST_ALREADY_EXISTS = ("6004", "Funding request already exists")
    FUNDING_REQUEST_DOES_NOT_EXISTS = ("6005", "Funding request doesn't exists")
    DATE_RANGE_EXCEEDS_LIMIT = (
        "6006",
        "Date range cannot exceed 7 days. Please provide a smaller date range.",
    )
