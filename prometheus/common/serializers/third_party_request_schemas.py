from marshmallow import Schema, fields


class HotelSchema(Schema):
    name = fields.String()
    email = fields.String()
    number = fields.String()


class BookingOwner(Schema):
    name = fields.String()
    salutation = fields.String()


class BookingSchema(Schema):
    channel_code = fields.String()


class InvoiceEmailDetailsSchema(Schema):
    id = fields.String()
    hotel = fields.Nested(HotelSchema)
    booking = fields.Nested(BookingSchema)
    adult_count = fields.Integer()
    child_count = fields.Integer()
    room_count = fields.Integer()
    checkin_date = fields.String()
    checkout_date = fields.String()
    booking_owner = fields.Nested(BookingOwner)
    guest_count = fields.Integer()
