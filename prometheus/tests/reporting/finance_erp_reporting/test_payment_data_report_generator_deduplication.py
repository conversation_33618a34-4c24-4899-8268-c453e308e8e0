import unittest
from unittest.mock import Mock, patch
from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.dto.finance_erp_dtos import PaymentChargeDetailsDto
from prometheus.reporting.finance_erp_reporting.external_finance_reports.payment_gateway_report.payment_gateway_report_generator import (
    PaymentDataReportGenerator,
)


class TestPaymentDataReportGeneratorDeduplication(unittest.TestCase):
    """
    Test cases to verify that payment redistribution scenarios do not cause
    duplicate payment pushes to finance ERP.
    """

    def setUp(self):
        self.generator = PaymentDataReportGenerator(bill_ids=[])

    def test_deduplicate_payments_single_split(self):
        """Test that single split payments are not affected by deduplication."""
        # Create a mock payment charge detail with single split
        payment_data = Mock()
        payment_data.bill_id = 1
        payment_data.payment_id = 100
        payment_data.payment_split_id = 1
        payment_data.amount = Money(Decimal('1000'), CurrencyType.INR)
        payment_data.payment_ref_id = 'ref_123'

        payments_list = [payment_data]
        
        result = self.generator._deduplicate_payments_by_payment_id(payments_list)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].amount, Money(Decimal('1000'), CurrencyType.INR))

    def test_deduplicate_payments_multiple_splits_same_payment(self):
        """Test that multiple splits for the same payment are consolidated."""
        # Create mock payment charge details for the same payment with multiple splits
        payment_data_1 = Mock()
        payment_data_1.bill_id = 1
        payment_data_1.payment_id = 100
        payment_data_1.payment_split_id = 1
        payment_data_1.amount = Money(Decimal('600'), CurrencyType.INR)
        payment_data_1.payment_ref_id = 'ref_123'

        payment_data_2 = Mock()
        payment_data_2.bill_id = 1
        payment_data_2.payment_id = 100
        payment_data_2.payment_split_id = 2
        payment_data_2.amount = Money(Decimal('400'), CurrencyType.INR)
        payment_data_2.payment_ref_id = 'ref_123'

        payments_list = [payment_data_1, payment_data_2]
        
        result = self.generator._deduplicate_payments_by_payment_id(payments_list)
        
        # Should have only one payment entry with consolidated amount
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].amount, Money(Decimal('1000'), CurrencyType.INR))
        # Should keep metadata from the split with higher split_id
        self.assertEqual(result[0].payment_split_id, 2)

    def test_deduplicate_payments_redistribution_scenario(self):
        """Test redistribution scenario where old splits are replaced with new ones."""
        # Simulate redistribution: old split (id=1) deleted, new splits (id=3,4) created
        # In real scenario, deleted splits wouldn't appear in the query results,
        # but this tests the consolidation logic
        
        # New split 1 after redistribution
        payment_data_1 = Mock()
        payment_data_1.bill_id = 1
        payment_data_1.payment_id = 100
        payment_data_1.payment_split_id = 3
        payment_data_1.amount = Money(Decimal('700'), CurrencyType.INR)
        payment_data_1.payment_ref_id = 'ref_123'

        # New split 2 after redistribution
        payment_data_2 = Mock()
        payment_data_2.bill_id = 1
        payment_data_2.payment_id = 100
        payment_data_2.payment_split_id = 4
        payment_data_2.amount = Money(Decimal('300'), CurrencyType.INR)
        payment_data_2.payment_ref_id = 'ref_123'

        payments_list = [payment_data_1, payment_data_2]
        
        result = self.generator._deduplicate_payments_by_payment_id(payments_list)
        
        # Should have only one payment entry with consolidated amount
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].amount, Money(Decimal('1000'), CurrencyType.INR))
        # Should keep metadata from the split with higher split_id
        self.assertEqual(result[0].payment_split_id, 4)

    def test_deduplicate_payments_different_payments(self):
        """Test that different payments are not consolidated."""
        # Create mock payment charge details for different payments
        payment_data_1 = Mock()
        payment_data_1.bill_id = 1
        payment_data_1.payment_id = 100
        payment_data_1.payment_split_id = 1
        payment_data_1.amount = Money(Decimal('1000'), CurrencyType.INR)
        payment_data_1.payment_ref_id = 'ref_123'

        payment_data_2 = Mock()
        payment_data_2.bill_id = 1
        payment_data_2.payment_id = 200  # Different payment_id
        payment_data_2.payment_split_id = 1
        payment_data_2.amount = Money(Decimal('500'), CurrencyType.INR)
        payment_data_2.payment_ref_id = 'ref_456'

        payments_list = [payment_data_1, payment_data_2]
        
        result = self.generator._deduplicate_payments_by_payment_id(payments_list)
        
        # Should have two separate payment entries
        self.assertEqual(len(result), 2)
        amounts = [payment.amount for payment in result]
        self.assertIn(Money(Decimal('1000'), CurrencyType.INR), amounts)
        self.assertIn(Money(Decimal('500'), CurrencyType.INR), amounts)

    def test_deduplicate_payments_different_bills(self):
        """Test that payments from different bills are not consolidated."""
        # Create mock payment charge details for same payment_id but different bills
        payment_data_1 = Mock()
        payment_data_1.bill_id = 1
        payment_data_1.payment_id = 100
        payment_data_1.payment_split_id = 1
        payment_data_1.amount = Money(Decimal('1000'), CurrencyType.INR)
        payment_data_1.payment_ref_id = 'ref_123'

        payment_data_2 = Mock()
        payment_data_2.bill_id = 2  # Different bill_id
        payment_data_2.payment_id = 100  # Same payment_id
        payment_data_2.payment_split_id = 1
        payment_data_2.amount = Money(Decimal('500'), CurrencyType.INR)
        payment_data_2.payment_ref_id = 'ref_456'

        payments_list = [payment_data_1, payment_data_2]
        
        result = self.generator._deduplicate_payments_by_payment_id(payments_list)
        
        # Should have two separate payment entries
        self.assertEqual(len(result), 2)
        amounts = [payment.amount for payment in result]
        self.assertIn(Money(Decimal('1000'), CurrencyType.INR), amounts)
        self.assertIn(Money(Decimal('500'), CurrencyType.INR), amounts)


if __name__ == '__main__':
    unittest.main()
