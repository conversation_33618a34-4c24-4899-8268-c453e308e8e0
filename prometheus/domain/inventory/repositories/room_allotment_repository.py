from collections import defaultdict
from typing import Dict, List, Set

from sqlalchemy import and_, func, or_
from sqlalchemy.orm.exc import NoResultFound
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.catalog.models import RoomModel
from prometheus.domain.inventory.adaptors.housekeeping_record_adaptor import (
    HouseKeepingRecordAdaptor,
)
from prometheus.domain.inventory.adaptors.room_inventory_adaptor import (
    RoomInventoryAdaptor,
)
from prometheus.domain.inventory.aggregates.room_allotment_aggregate import (
    RoomAllotmentAggregate,
)
from prometheus.domain.inventory.dtos.house_status_dto import HouseStatusDto
from prometheus.domain.inventory.entities.room_allotment import RoomAllotment
from prometheus.domain.inventory.entities.room_inventory import RoomInventory
from prometheus.domain.inventory.models import (
    HouseKeepingRecordModel,
    RoomAllotmentModel,
    RoomInventoryAvailabilityModel,
    RoomInventoryModel,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.catalog_constants import RoomStatus as CatalogRoomStatus
from ths_common.constants.inventory_constants import (
    AllottedFor,
    HouseKeepingStatus,
    RoomCurrentStatus,
    RoomStatus,
)
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import InvalidOperationError


@register_instance()
class RoomAllotmentRepository(BaseRepository):
    room_inventory_adaptor = RoomInventoryAdaptor()
    housekeeping_record_adaptor = HouseKeepingRecordAdaptor()

    def to_aggregate(self, **kwargs):
        room_allotment_models = kwargs.get('room_allotment_models')
        room_allotments = []
        room_inventory_model = kwargs.get('room_inventory_model')
        if room_inventory_model:
            room_inventory = self.room_inventory_adaptor.to_domain_entity(
                room_inventory_model
            )
        else:
            room_inventory = RoomInventory(
                hotel_id=kwargs.get('hotel_id'), room_id=kwargs.get('room_id')
            )
        if room_allotment_models:
            for room_allotment_model in room_allotment_models:
                room_allotments.append(
                    RoomAllotment(
                        allotment_id=room_allotment_model.allotment_id,
                        start_time=dateutils.localize_datetime(
                            room_allotment_model.start_time
                        ),
                        actual_start_time=dateutils.localize_datetime(
                            room_allotment_model.actual_start_time
                        )
                        if room_allotment_model.actual_start_time
                        else None,
                        expected_end_time=dateutils.localize_datetime(
                            room_allotment_model.expected_end_time
                        ),
                        actual_end_time=dateutils.localize_datetime(
                            room_allotment_model.actual_end_time
                        )
                        if room_allotment_model.actual_end_time
                        else None,
                        allotted_for=AllottedFor(room_allotment_model.allotted_for),
                        deleted=room_allotment_model.deleted,
                        dirty=False,
                        new=False,
                    )
                )

        housekeeping_record_model = kwargs.get('housekeeping_record_model')
        housekeeping_record = (
            self.housekeeping_record_adaptor.to_domain_entity(housekeeping_record_model)
            if housekeeping_record_model
            else None
        )
        room_allotment_aggregate = RoomAllotmentAggregate(
            room_inventory=room_inventory,
            housekeeping_record=housekeeping_record,
            room_allotments=room_allotments,
        )
        return room_allotment_aggregate

    def from_aggregate(self, aggregate: RoomAllotmentAggregate = None):
        room_allotments = aggregate.all_room_allotments
        room_allotment_models = []
        room_inventory_model = self.room_inventory_adaptor.to_db_entity(
            aggregate.room_inventory
        )
        hotel_id, room_id = (
            aggregate.room_inventory.hotel_id,
            aggregate.room_inventory.room_id,
        )
        if room_allotments:
            for room_allotment in room_allotments:
                room_allotment_models.append(
                    RoomAllotmentModel(
                        allotment_id=room_allotment.allotment_id,
                        hotel_id=hotel_id,
                        room_id=room_id,
                        start_time=room_allotment.start_time,
                        actual_start_time=room_allotment.actual_start_time,
                        expected_end_time=room_allotment.expected_end_time,
                        actual_end_time=room_allotment.actual_end_time,
                        allotted_for=room_allotment.allotted_for.value,
                        deleted=room_allotment.deleted,
                    )
                )
        housekeeping_record_model = None
        if aggregate.housekeeping_record:
            housekeeping_record_model = self.housekeeping_record_adaptor.to_db_entity(
                aggregate.housekeeping_record
            )
        return room_allotment_models, housekeeping_record_model, room_inventory_model

    def get_updated_models(self, aggregate: RoomAllotmentAggregate):
        room_allotments = aggregate.all_room_allotments
        hotel_id, room_id = (
            aggregate.room_inventory.hotel_id,
            aggregate.room_inventory.room_id,
        )
        room_inventory_model = (
            self.room_inventory_adaptor.to_db_entity(aggregate.room_inventory)
            if aggregate.room_inventory.has_changes_to_be_saved()
            else None
        )
        housekeeping_record_model = None
        if (
            aggregate.housekeeping_record
            and aggregate.housekeeping_record.has_changes_to_be_saved()
        ):
            housekeeping_record_model = self.housekeeping_record_adaptor.to_db_entity(
                aggregate.housekeeping_record
            )

        room_allotment_models = [
            RoomAllotmentModel(
                allotment_id=ra.allotment_id,
                hotel_id=hotel_id,
                room_id=room_id,
                start_time=ra.start_time,
                actual_start_time=ra.actual_start_time,
                expected_end_time=ra.expected_end_time,
                actual_end_time=ra.actual_end_time,
                allotted_for=ra.allotted_for.value,
                deleted=ra.deleted,
            )
            for ra in room_allotments
            if ra.has_changes_to_be_saved()
        ]
        return room_allotment_models, housekeeping_record_model, room_inventory_model

    def save(self, room_allotment_aggregate):
        (
            room_allotment_models,
            housekeeping_record_model,
            room_inventory_model,
        ) = self.from_aggregate(aggregate=room_allotment_aggregate)
        self._save_all(room_allotment_models)
        if housekeeping_record_model:
            self._update(housekeeping_record_model)
        if room_inventory_model:
            self._update(room_inventory_model)

        self._track_room_availability_or_dnr_status(room_allotment_aggregate)

        self.flush_session()

    def save_all(self, room_allotment_aggregates):
        for room_allotment_aggregate in room_allotment_aggregates:
            self.save(room_allotment_aggregate)
        self.flush_session()

    def update_all(self, room_allotment_aggregates):
        for room_allotment_aggregate in room_allotment_aggregates:
            self.update(room_allotment_aggregate)
        self.flush_session()

    def update(self, room_allotment_aggregate):
        (
            room_allotment_models,
            housekeeping_record_model,
            room_inventory_model,
        ) = self.get_updated_models(aggregate=room_allotment_aggregate)
        self._update_all(room_allotment_models)
        if housekeeping_record_model:
            self._update(housekeeping_record_model)
        if room_inventory_model:
            self._update(room_inventory_model)

        self._track_room_availability_or_dnr_status(room_allotment_aggregate)

        self.flush_session()

    def _track_room_availability_or_dnr_status(
        self, room_allotment_aggregate: RoomAllotmentAggregate
    ):
        # (Rohit) Date: 26th Oct 2021 -> House Status Implementation
        # Start using RoomInventoryAvailability model to keep track of current room status on date basis
        # Could have added DNR in RoomCurrentStatus, but that is currently used, and can affect lot of flow.
        # This model was initially added, but is not used anywhere now. So safer to start storing it here.
        # Also, this model can then give EOD report for previous business date too.
        business_date = crs_context.get_hotel_context().current_date()
        room_inventory_status_on_current_business_date = (
            self.query(RoomInventoryAvailabilityModel)
            .filter(
                RoomInventoryAvailabilityModel.hotel_id
                == room_allotment_aggregate.room_inventory.hotel_id,
                RoomInventoryAvailabilityModel.room_id
                == room_allotment_aggregate.room_inventory.room_id,
                RoomInventoryAvailabilityModel.date == business_date,
            )
            .first()
        )

        if (
            room_allotment_aggregate.room_inventory.current_status
            == RoomCurrentStatus.OCCUPIED
        ):
            room_inventory_availability_status = RoomStatus.BUSY.value
        elif (
            room_allotment_aggregate.housekeeping_record.housekeeping_status
            == HouseKeepingStatus.OUT_OF_ORDER
        ):
            room_inventory_availability_status = RoomStatus.DNR.value
        else:
            room_inventory_availability_status = RoomStatus.AVAILABLE.value

        if not room_inventory_status_on_current_business_date:
            room_inventory_status_on_current_business_date = (
                RoomInventoryAvailabilityModel(
                    hotel_id=room_allotment_aggregate.room_inventory.hotel_id,
                    room_id=room_allotment_aggregate.room_inventory.room_id,
                    date=business_date,
                    status=room_inventory_availability_status,
                )
            )
            self._save(room_inventory_status_on_current_business_date)
        else:
            room_inventory_status_on_current_business_date.status = (
                room_inventory_availability_status
            )
            self._update(room_inventory_status_on_current_business_date)

    def load(self, hotel_id, room_id, for_update=False, start_time=None, end_time=None):
        room_inventory_model = (
            self.filter(RoomInventoryModel, for_update=for_update, nowait=False)
            .filter(
                RoomInventoryModel.hotel_id == hotel_id,
                RoomInventoryModel.room_id == room_id,
            )
            .first()
        )

        q = (
            self.filter(RoomAllotmentModel)
            .filter(RoomAllotmentModel.hotel_id == hotel_id)
            .filter(RoomAllotmentModel.room_id == room_id)
        )

        if end_time:
            q = q.filter(RoomAllotmentModel.start_time <= end_time)

        if start_time:
            q = q.filter(
                or_(
                    RoomAllotmentModel.actual_end_time >= start_time,
                    and_(
                        RoomAllotmentModel.actual_end_time == None,
                        RoomAllotmentModel.expected_end_time >= start_time,
                    ),
                )
            )

        room_allotment_models = q.all()
        housekeeping_record_model = self.filter(
            HouseKeepingRecordModel,
            HouseKeepingRecordModel.hotel_id == hotel_id,
            HouseKeepingRecordModel.room_id == room_id,
        ).all()

        housekeeping_record_model = (
            housekeeping_record_model[0] if len(housekeeping_record_model) > 0 else None
        )
        room_allotment_aggregate = self.to_aggregate(
            hotel_id=hotel_id,
            room_id=room_id,
            housekeeping_record_model=housekeeping_record_model,
            room_inventory_model=room_inventory_model,
            room_allotment_models=room_allotment_models,
        )
        return room_allotment_aggregate

    def load_for_update(self, hotel_id, room_id):
        return self.load(hotel_id, room_id, for_update=True)

    def load_multiple(
        self,
        hotel_id,
        room_ids=None,
        start_time=None,
        end_time=None,
        skip_allotments=False,
        for_update=False,
    ):
        q = self.filter(RoomInventoryModel, for_update=for_update, nowait=False).filter(
            RoomInventoryModel.hotel_id == hotel_id
        )
        if room_ids:
            q = q.filter(RoomInventoryModel.room_id.in_(room_ids))
        room_inventory_models = q.order_by(
            RoomInventoryModel.hotel_id, RoomInventoryModel.room_id
        ).all()
        grouped_room_inventory_models = {
            room_inv_model.room_id: room_inv_model
            for room_inv_model in room_inventory_models
        }

        grouped_room_allotment_models = defaultdict(list)
        if not skip_allotments:
            q = self.filter(RoomAllotmentModel).filter(
                RoomAllotmentModel.hotel_id == hotel_id
            )

            if room_ids:
                q = q.filter(RoomAllotmentModel.room_id.in_(room_ids))

            if end_time:
                q = q.filter(RoomAllotmentModel.start_time <= end_time)

            if start_time:
                q = q.filter(
                    or_(
                        RoomAllotmentModel.actual_end_time >= start_time,
                        and_(
                            RoomAllotmentModel.actual_end_time == None,
                            RoomAllotmentModel.expected_end_time >= start_time,
                        ),
                    )
                )

            room_allotment_models = q.all()
            for room_allotment_model in room_allotment_models:
                grouped_room_allotment_models[room_allotment_model.room_id].append(
                    room_allotment_model
                )

        q = self.filter(
            HouseKeepingRecordModel, HouseKeepingRecordModel.hotel_id == hotel_id
        )
        if room_ids:
            q = q.filter(HouseKeepingRecordModel.room_id.in_(room_ids))
        housekeeping_record_models = q.all()
        grouped_housekeeping_record_models = {
            housekeeping_record_model.room_id: housekeeping_record_model
            for housekeeping_record_model in housekeeping_record_models
        }

        grouped_room_allotment_aggregates = dict()
        for room_id, room_inventory_model in grouped_room_inventory_models.items():
            housekeeping_record_model = grouped_housekeeping_record_models.get(room_id)
            room_allotment_models = grouped_room_allotment_models.get(room_id)
            room_inventory_model = grouped_room_inventory_models.get(room_id)
            room_allotment_aggregate = self.to_aggregate(
                hotel_id=hotel_id,
                room_id=room_id,
                housekeeping_record_model=housekeeping_record_model,
                room_inventory_model=room_inventory_model,
                room_allotment_models=room_allotment_models,
            )
            grouped_room_allotment_aggregates[room_id] = room_allotment_aggregate
        return grouped_room_allotment_aggregates

    def load_room_inventories(self, hotel_id, room_ids=None, for_update=False):
        room_inventory_models = self.filter(
            RoomInventoryModel, for_update=for_update, nowait=False
        ).filter(RoomInventoryModel.hotel_id == hotel_id)

        if room_ids:
            room_inventory_models = room_inventory_models.filter(
                RoomInventoryModel.room_id.in_(room_ids)
            )

        return [
            self.room_inventory_adaptor.to_domain_entity(room_inventory_model)
            for room_inventory_model in room_inventory_models
        ]

    def save_room_inventories(self, room_inventories):
        # Only used from ITests
        room_inventory_models = [
            self.room_inventory_adaptor.to_db_entity(room_inventory)
            for room_inventory in room_inventories
        ]
        self._update_all(room_inventory_models)
        self.flush_session()

    def save_housekeeping_records(self, housekeeping_records):
        # Only used from ITests
        housekeeping_record_models = [
            self.housekeeping_record_adaptor.to_db_entity(housekeeping_record)
            for housekeeping_record in housekeeping_records
        ]
        self._update_all(housekeeping_record_models)
        self.flush_session()

    def load_all_housekeeping_records(self, hotel_id):
        room_inventory_models = (
            self.filter(RoomInventoryModel)
            .filter(RoomInventoryModel.hotel_id == hotel_id)
            .all()
        )
        grouped_room_inventory_models = {
            room_inv_model.room_id: room_inv_model
            for room_inv_model in room_inventory_models
        }
        housekeeping_record_models = self.filter(
            HouseKeepingRecordModel, HouseKeepingRecordModel.hotel_id == hotel_id
        ).all()
        room_allotment_aggregates = []
        for housekeeping_record_model in housekeeping_record_models:
            room_inventory_model = grouped_room_inventory_models.get(
                housekeeping_record_model.room_id
            )
            room_allotment_aggregate = self.to_aggregate(
                hotel_id=hotel_id,
                room_id=housekeeping_record_model.room_id,
                housekeeping_record_model=housekeeping_record_model,
                room_inventory_model=room_inventory_model,
                room_allotment_models=None,
            )
            room_allotment_aggregates.append(room_allotment_aggregate)
        return room_allotment_aggregates

    def delete_allotments_for_hotel_id(self, hotel_id, user_data):
        if not user_data or user_data.user_type != UserType.CRS_MIGRATION_USER.value:
            raise InvalidOperationError(
                "Only CRS Migration User can delete room allotments"
            )

        deleted_room_allotment_count = (
            self.query(RoomAllotmentModel)
            .filter(RoomAllotmentModel.hotel_id == hotel_id)
            .delete(synchronize_session=False)
        )
        self.flush_session()
        return deleted_room_allotment_count

    def delete_room_allotments_for_allotment_ids(self, allotment_ids):
        self.query(RoomAllotmentModel).filter(
            RoomAllotmentModel.allotment_id.in_(allotment_ids)
        ).update({'deleted': True}, synchronize_session=False)

    def load_checked_in_room_allotments(self, hotel_id, room_ids):
        q = (
            self.filter(RoomAllotmentModel)
            .filter(RoomAllotmentModel.hotel_id == hotel_id)
            .filter(RoomAllotmentModel.room_id.in_(room_ids))
        )

        q = q.filter(
            and_(
                RoomAllotmentModel.start_time < dateutils.current_datetime(),
                RoomAllotmentModel.actual_end_time == None,
                RoomAllotmentModel.deleted == False,
            )
        )
        room_allotment_models = q.all()
        grouped_room_allotment_models = defaultdict(list)
        for room_allotment_model in room_allotment_models:
            grouped_room_allotment_models[room_allotment_model.room_id].append(
                room_allotment_model
            )
        return grouped_room_allotment_models

    def get_rooms_grouped_by_current_status(
        self, hotel_id, business_date, exclude_room_type_id=None
    ) -> Dict[RoomStatus, Set]:
        query = (
            self.query(
                RoomInventoryAvailabilityModel.room_id,
                RoomInventoryAvailabilityModel.status,
            )
            .join(
                RoomModel,
                and_(
                    RoomModel.hotel_id == RoomInventoryAvailabilityModel.hotel_id,
                    RoomModel.room_id == RoomInventoryAvailabilityModel.room_id,
                ),
            )
            .filter(
                RoomInventoryAvailabilityModel.hotel_id == hotel_id,
                RoomInventoryAvailabilityModel.date == business_date,
                RoomModel.status == 'active',
            )
        )

        if exclude_room_type_id:
            query = query.filter(RoomModel.room_type_id != exclude_room_type_id)

        room_availability_status_tuples = query.all()

        status_wise_room_ids = defaultdict(set)
        # TODO: Should we filter inactive rooms here?
        for room_availability_status in room_availability_status_tuples:
            status_wise_room_ids[room_availability_status[1]].add(
                room_availability_status[0]
            )
        return status_wise_room_ids

    def get_house_status(
        self, hotel_id, exclude_room_type_id=None, exclude_inactive_rooms=None
    ) -> List[HouseStatusDto]:
        query = self.filter(RoomInventoryModel, RoomInventoryModel.hotel_id == hotel_id)

        if exclude_room_type_id or exclude_inactive_rooms:
            query = query.join(
                RoomModel,
                and_(
                    RoomModel.hotel_id == RoomInventoryModel.hotel_id,
                    RoomModel.room_id == RoomInventoryModel.room_id,
                ),
            )

            if exclude_room_type_id:
                query = query.filter(RoomModel.room_type_id != exclude_room_type_id)

            if exclude_inactive_rooms:
                query = query.filter(
                    RoomModel.status != CatalogRoomStatus.INACTIVE.value
                )

        room_inventories = [
            self.room_inventory_adaptor.to_domain_entity(room_inventory_model)
            for room_inventory_model in query.all()
        ]

        housekeeping_record_models = self.filter(
            HouseKeepingRecordModel, HouseKeepingRecordModel.hotel_id == hotel_id
        ).all()
        grouped_housekeeping_records = {
            hk_record_model.room_id: self.housekeeping_record_adaptor.to_domain_entity(
                hk_record_model
            )
            for hk_record_model in housekeeping_record_models
        }

        return [
            HouseStatusDto(
                room_id=room_inventory.room_id,
                reservation_statuses=room_inventory.reservation_statuses,
                housekeeping_status=grouped_housekeeping_records.get(
                    room_inventory.room_id
                ).housekeeping_status
                if room_inventory.room_id in grouped_housekeeping_records
                else HouseKeepingStatus.CLEAN,
            )
            for room_inventory in room_inventories
        ]

    def count_housekeeping_status_rooms(self, hotel_id):
        rooms_housekeeping_status_count = (
            self.query(
                RoomModel.room_type_id,
                HouseKeepingRecordModel.housekeeping_status,
                func.count(RoomModel.room_type_id).label("room_count"),
            )
            .join(
                HouseKeepingRecordModel,
                and_(
                    HouseKeepingRecordModel.hotel_id == RoomModel.hotel_id,
                    HouseKeepingRecordModel.room_id == RoomModel.room_id,
                ),
            )
            .filter(
                RoomModel.hotel_id == hotel_id,
                or_(
                    HouseKeepingRecordModel.housekeeping_status
                    == HouseKeepingStatus.OUT_OF_ORDER.value,
                    HouseKeepingRecordModel.housekeeping_status
                    == HouseKeepingStatus.OUT_OF_SERVICE.value,
                ),
                HouseKeepingRecordModel.deleted == False,
                RoomModel.deleted == False,
            )
            .group_by(
                RoomModel.room_type_id, HouseKeepingRecordModel.housekeeping_status
            )
            .all()
        )

        rooms_housekeeping_status_count_map = defaultdict(dict)
        for room_housekeeping_status_count in rooms_housekeeping_status_count:
            rooms_housekeeping_status_count_map[
                room_housekeeping_status_count.room_type_id
            ].update(
                {
                    room_housekeeping_status_count.housekeeping_status: room_housekeeping_status_count.room_count
                }
            )

        return rooms_housekeeping_status_count_map

    def load_checked_in_room_allotment(self, hotel_id, room_no):
        try:
            q = (
                self.filter(RoomAllotmentModel)
                .join(
                    RoomModel,
                    and_(
                        RoomModel.hotel_id == RoomAllotmentModel.hotel_id,
                        RoomModel.room_id == RoomAllotmentModel.room_id,
                    ),
                )
                .filter(
                    RoomModel.hotel_id == hotel_id,
                    RoomModel.room_number == room_no,
                    RoomAllotmentModel.start_time < dateutils.current_datetime(),
                    RoomAllotmentModel.actual_end_time.is_(None),
                    RoomAllotmentModel.deleted.is_(False),
                )
            )
            return q.one()
        except NoResultFound:
            return None

    def load_room_allotment_between_date_range(
        self, hotel_id, room_no, start_time, end_time
    ):
        try:
            q = (
                self.filter(RoomAllotmentModel)
                .join(
                    RoomModel,
                    and_(
                        RoomModel.hotel_id == RoomAllotmentModel.hotel_id,
                        RoomModel.room_id == RoomAllotmentModel.room_id,
                    ),
                )
                .filter(
                    RoomModel.room_number == room_no,
                    RoomAllotmentModel.hotel_id == hotel_id,
                    RoomAllotmentModel.start_time <= end_time,
                    RoomAllotmentModel.expected_end_time >= start_time,
                )
            )
            room_allotment_models = q.all()
            return room_allotment_models
        except NoResultFound:
            return None
