from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.command_handlers.crs.update_payments import (
    CrsUpdatePaymentsCommandHandler,
)
from prometheus.application.billing.command_handlers.pos.update_payments import (
    PosUpdatePaymentsCommandHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import BillRepository
from ths_common.constants.billing_constants import BillAppId


@register_instance(
    dependencies=[
        BillRepository,
        PosUpdatePaymentsCommandHandler,
        CrsUpdatePaymentsCommandHandler,
    ]
)
class UpdatePaymentsCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        pos_update_payments_command_handler: PosUpdatePaymentsCommandHandler,
        crs_update_payments_command_handler: CrsUpdatePaymentsCommandHandler,
    ):
        self.bill_repository = bill_repository
        self.pos_update_payments_command_handler = pos_update_payments_command_handler
        self.crs_update_payments_command_handler = crs_update_payments_command_handler

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, bill_id, version, payment_dtos, user_data, hotel_aggregate=None):
        bill_aggregate = self.bill_repository.load_for_update(bill_id)
        crs_context.set_current_bill(bill_aggregate)

        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            return self.pos_update_payments_command_handler.handle(
                bill_aggregate, payment_dtos
            )
        elif bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
            return self.crs_update_payments_command_handler.handle(
                bill_aggregate, payment_dtos, user_data
            )
