import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.common.serializers.third_party_request_schemas import (
    InvoiceEmailDetailsSchema,
)
from prometheus.domain.billing.repositories import (
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationSenderName,
    NotificationServiceClient,
)
from prometheus.infrastructure.external_clients.notification_service_constants import (
    ApprovedWhatsappTemplate,
    WhatsappMessageType,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from ths_common.utils.common_utils import is_valid_email
from ths_common.value_objects import EmailAttachment, InvoiceEmailData, PhoneNumber

logger = logging.getLogger(__name__)


@register_instance(dependencies=[InvoiceRepository, CreditNoteRepository])
class InvoiceMailer:
    def __init__(self, invoice_repository, credit_note_repository):
        self.invoice_repository = invoice_repository
        self.credit_note_repository = credit_note_repository

    @staticmethod
    def is_sending_email_required(booking_aggregate, hotel_context, invoice_aggregate):
        has_non_credit_charge = invoice_aggregate.has_invoice_credit_charge()
        if not crs_context.is_treebo_tenant():
            return False
        if booking_aggregate.is_b2b_booking() and has_non_credit_charge:
            return True
        return (
            not booking_aggregate.is_b2b_ta_booking()
            and not hotel_context.is_independent_hotel()
        )

    @staticmethod
    def subject_line_for_invoice_email(reference_number, hotel_name):
        return "Invoice for booking {reference_number} in {hotel_name}".format(
            reference_number=reference_number, hotel_name=hotel_name
        )

    @staticmethod
    def attachments_for_invoice_email(invoice_aggregates, credit_note_aggregates):
        attachments = []
        for invoice_aggregate in invoice_aggregates:
            attachments.append(
                EmailAttachment(
                    url=invoice_aggregate.signed_url,
                    filename='{}.pdf'.format(invoice_aggregate.invoice_number),
                )
            )
        if credit_note_aggregates:
            attachments.append(
                EmailAttachment(
                    url=credit_note_aggregate.signed_url,
                    filename='{}.pdf'.format(credit_note_aggregate.credit_note_number),
                )
                for credit_note_aggregate in credit_note_aggregates
            )

        return attachments

    def email_invoices_and_credit_notes(
        self,
        invoice_aggregates,
        booking_aggregate,
        subject_line,
        receiver_email,
        credit_note_aggregates,
    ) -> JobResultDto:
        hotel_id = invoice_aggregates[0].invoice.vendor_details.hotel_id

        crs_context_middleware.set_hotel_context(hotel_id)
        booking_details = {
            "channel_code": booking_aggregate.booking.source.channel_code,
            "reference_number": booking_aggregate.booking.reference_number,
            "checkin_date": booking_aggregate.booking.checkin_date,
            "checkout_date": booking_aggregate.booking.checkout_date,
            "room_count": len(booking_aggregate.get_active_room_stays()),
        }
        guest_details = {
            "adult_count": sum(
                len(rs.adult_guest_stays())
                for rs in booking_aggregate.get_active_room_stays()
            ),
            "child_count": sum(
                len(rs.child_guest_stays())
                for rs in booking_aggregate.get_active_room_stays()
            ),
            "guest_count": sum(
                len(rs.all_guest_stays_except_cancelled())
                for rs in booking_aggregate.get_active_room_stays()
            ),
            "owner_name": booking_aggregate.get_booking_owner().first_name,
            "owner_salutation": booking_aggregate.get_booking_owner().salutation,
        }
        hotel_context = crs_context.get_hotel_context()
        hotel_details = {
            "name": hotel_context.hotel_name,
            "email": hotel_context.email,
            "number": hotel_context.phone_number,
        }
        invoice_email_json_data = (
            InvoiceEmailDetailsSchema()
            .dump(InvoiceEmailData(hotel_details, booking_details, guest_details))
            .data
        )
        logger.debug("template request json: %s", invoice_email_json_data)
        response = TemplateService().generate_email_body(
            TemplateNameSpace.PMS_SHARE_INVOICE.value,
            invoice_email_json_data,
            TemplateFormat.HTML,
        )
        logger.debug("template response: %s", response)

        if is_valid_email(receiver_email):
            NotificationServiceClient().email(
                body_html=response,
                subject=subject_line,
                sender=NotificationEmailIds.NOREPLY.value,
                recievers=[receiver_email],
                attachments=self.attachments_for_invoice_email(
                    invoice_aggregates, credit_note_aggregates
                ),
                sender_name=NotificationSenderName.TREEBO_HOTELS.value,
                raise_on_failure=False,
            )
        return JobResultDto.success()

    @staticmethod
    def send_invoices_on_whatsapp(invoice_aggregates, receiver_phone: PhoneNumber):
        for invoice_aggregate in invoice_aggregates:
            NotificationServiceClient().whatsapp(
                message_type=WhatsappMessageType.DOCUMENT.value,
                receivers=[receiver_phone.number],
                media_url=invoice_aggregate.signed_url,
                caption=ApprovedWhatsappTemplate.INVOICE_ATTACHMENT.value,
                filename='{}.pdf'.format(invoice_aggregate.invoice_number),
            )

    def send_invoices_to_guest(
        self, invoice_aggregates, booking_aggregate, credit_note_aggregates=None
    ):
        if invoice_aggregates:
            subject_line = self.subject_line_for_invoice_email(
                booking_aggregate.booking.reference_number,
                crs_context.get_hotel_context().hotel_name,
            )
            receiver_email = booking_aggregate.get_receiver_email_for_invoice()
            receiver_phone = booking_aggregate.get_receiver_phone_number_for_invoice()

            self.email_invoices_and_credit_notes(
                invoice_aggregates,
                booking_aggregate,
                subject_line,
                receiver_email,
                credit_note_aggregates,
            )
            if receiver_phone:
                self.send_invoices_on_whatsapp(invoice_aggregates, receiver_phone)
