import logging

from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.command_handlers.invoice.helpers.preview_invoice_helpers import (
    get_checkout_datetime,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.guest_communication.invoice_mailer import (
    InvoiceMailer,
)
from prometheus.application.booking.helpers.invoice_confirmation_service import (
    InvoiceConfirmationService,
)
from prometheus.application.commands.consume_charge import ConsumeChargeCommand
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.attachment_service import AttachmentService
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.decorators import bypass_access_entity_checks
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.allowance_data import EditAllowanceData
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.booking.factories.booking_action_factory import (
    BookingActionFactory,
)
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.booking.services.booking_checkout_request_generator import (
    BookingCheckoutRequestGenerator,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.inventory_requirement import InventoryRequirement
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import ChargeStatus, InvoiceStatus
from ths_common.constants.booking_constants import (
    AttachmentGroup,
    AttachmentStatus,
    BookingActions,
    BookingStatus,
    InvoiceGroupStatus,
    ServiceTypes,
)
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.segment_constants import SegmentEvent
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import flatten_list
from ths_common.value_objects import (
    BillSideEffect,
    GroupedCancelledChargesDto,
    RoomStayChargesDto,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomTypeInventoryRepository,
        RoomTypeRepository,
        BookingActionRepository,
        RoomRepository,
        HotelRepository,
        BookingInvoiceGroupRepository,
        RoomStayOverflowRepository,
        InvoiceRepository,
        JobSchedulerService,
        HotelConfigRepository,
        NewrelicServiceClient,
        InvoiceConfirmationService,
        ExpenseItemRepository,
        CreditNoteRepository,
        SkuCategoryRepository,
        TaxService,
        TenantSettings,
        InventoryApplicationService,
        RoomStayOverflowService,
        InvoiceMailer,
        TACommissionHelper,
        AttachmentService,
        InventoryBlockRepository,
    ]
)
class CheckoutGuestCommandHandler:
    def __init__(
        self,
        booking_repository,
        bill_repository,
        room_type_inventory_repository,
        room_type_repository,
        booking_action_repository,
        room_repository,
        hotel_repository,
        booking_invoice_group_repository,
        room_stay_overflow_repository,
        invoice_repository,
        job_scheduler_service,
        hotel_config_repository,
        alerting_service,
        invoice_confirmation_service,
        expense_item_repository,
        credit_note_repository,
        sku_category_repository,
        tax_service,
        tenant_settings,
        inventory_application_service: InventoryApplicationService,
        room_stay_overflow_service,
        invoice_mailer: InvoiceMailer,
        ta_commission_helper: TACommissionHelper,
        attachment_service: AttachmentService,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.bill_repository = bill_repository
        self.room_type_repository = room_type_repository
        self.room_repository = room_repository
        self.invoice_repository = invoice_repository
        self.job_scheduler_service = job_scheduler_service
        self.room_allotment_repository = RoomAllotmentRepository()
        self.invoice_confirmation_service = invoice_confirmation_service
        self.room_type_inventory_repository = room_type_inventory_repository
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.alerting_service = alerting_service
        self.expense_item_repository = expense_item_repository
        self.credit_note_repository = credit_note_repository
        self.sku_category_repository = sku_category_repository
        self.tax_service = tax_service
        self.tenant_settings = tenant_settings
        self.inventory_application_service = inventory_application_service
        self.room_stay_overflow_service = room_stay_overflow_service
        self.invoice_mailer = invoice_mailer
        self.ta_commission_helper = ta_commission_helper
        self.attachment_service = attachment_service
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @audit(audit_type=AuditType.CHECKOUT_PERFORMED)
    @audit_housekeeping(action_performed="checkout")
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        checkout_action_payload,
        user_data,
        hotel_aggregate=None,
    ):
        """
        Note:
        1. Not marking pending charges consumed at this point. this is done in the preview request.
        TODO:
        1. Validation of charges in the invoice.
        :param booking_id:
        :param booking_version:
        :param checkout_action_payload:
        :param user_data:
        :param hotel_aggregate:
        :return:
        """
        logger.debug(
            "Checkout action received on booking: %s with payload: %s",
            booking_id,
            checkout_action_payload,
        )

        # Load everything
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, version=booking_version
        )
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None

        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(
                booking_aggregate.hotel_id
            )
            hotel_context = crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )

        checkout_datetime = get_checkout_datetime(hotel_context)

        bill_aggregate = self.bill_repository.load_for_update(booking_aggregate.bill_id)

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)

        RuleEngine.action_allowed(
            action="checkout",
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )

        booking_action_aggregate = BookingActionFactory.create_new_action(
            booking_id=booking_id,
            action_type=BookingActions.CHECKOUT,
            payload=checkout_action_payload,
            previous_state=booking_aggregate.booking.status,
            business_date=hotel_context.current_date(),
        )
        crs_context.set_current_action_id(booking_action_aggregate)
        updated_aggregates = dict()

        booking_invoice_group_id = checkout_action_payload.get('invoice_group_id')
        booking_invoice_group_aggregate = (
            self.booking_invoice_group_repository.load_for_update(
                booking_invoice_group_id
            )
        )

        # validations
        if booking_invoice_group_aggregate.status != InvoiceGroupStatus.PREVIEW:
            raise ValidationException(
                ApplicationErrors.INVOICE_GROUP_NOT_IN_PREVIEW_STATE
            )

        charges_to_be_posted = []
        allowances_to_be_posted = []
        allowances_to_exclude = []
        charges_to_exclude = []
        if (
            booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted
        ):
            charges_to_be_posted = booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted.get(
                'booked_charges_to_post', []
            )
            allowances_to_be_posted = booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted.get(
                'booked_allowances_to_post', []
            )
            charges_to_exclude = booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted.get(
                'booked_charge_to_exclude', []
            )
            allowances_to_exclude = booking_invoice_group_aggregate.booking_invoice_group.charges_and_allowances_to_be_posted.get(
                'booked_allowances_to_exclude', []
            )

        # Consume Charges
        if charges_to_be_posted:
            updated_charges = ConsumeChargeCommand(
                booking_aggregate,
                bill_aggregate,
                hotel_context.current_date(),
                hotel_context,
                self.room_type_repository.load_type_map(),
                is_checkout=True,
                checkout_date=dateutils.to_date(checkout_datetime),
            ).execute(charge_ids=charges_to_be_posted)

        # post allowances
        for allowance in allowances_to_be_posted:
            bill_aggregate.update_allowance(
                allowance.get('charge_id'),
                allowance.get('charge_split_id'),
                allowance.get('allowance_id'),
                EditAllowanceData(status=ChargeStatus.CONSUMED),
                posting_date=crs_context.get_hotel_context().current_date(),
            )

        # checkout guests
        checkout_request_per_rooms = self._perform_checkout(
            booking_invoice_group_aggregate, booking_aggregate, hotel_aggregate
        )

        (
            cancelled_charge_ids,
            grouped_cancelled_charges,
            service_cleanup_side_effects,
        ) = self._handle_unused_charges_after_checkout(
            checkout_request_per_rooms,
            booking_aggregate,
            bill_aggregate,
            charges_to_be_posted,
            charges_to_exclude,
            checkout_datetime,
        )
        booking_aggregate.cancel_expense_for_charges(cancelled_charge_ids)

        for allowance in allowances_to_exclude:
            bill_aggregate.update_allowance(
                allowance.get('charge_id'),
                allowance.get('charge_split_id'),
                allowance.get('allowance_id'),
                EditAllowanceData(status=ChargeStatus.CANCELLED),
                posting_date=crs_context.get_hotel_context().current_date(),
            )

        bill_side_effects = BillSideEffect(
            cancelled_charge_ids=cancelled_charge_ids,
            cancelled_allowances=allowances_to_exclude,
            grouped_cancelled_charges=grouped_cancelled_charges,
            added_charge_ids=booking_invoice_group_aggregate.booking_invoice_group.newly_added_charge_ids
            or [],
        )
        booking_action_aggregate.update_side_effect(bill_side_effect=bill_side_effects)

        addon_inventory_blocks_to_remove = None
        if service_cleanup_side_effects:
            self.inventory_block_repository.update_all(
                service_cleanup_side_effects.inventory_blocks_released
            )
            addon_inventory_blocks_to_remove = (
                service_cleanup_side_effects.inventory_blocks_released
            )

        self._update_inventory_after_checkout(
            booking_aggregate,
            checkout_request_per_rooms,
            hotel_aggregate,
            hotel_context,
            addon_inventory_blocks_to_remove=addon_inventory_blocks_to_remove,
            user_action="checkout",
        )
        self._schedule_lco_full_fill_job(booking_aggregate, checkout_request_per_rooms)

        room_stay_id_to_charge_id_map = booking_aggregate.get_room_stay_charge_id_map()
        if booking_invoice_group_aggregate.invoice_ids:
            invoice_aggregates = self.invoice_repository.load_all_for_update(
                booking_invoice_group_aggregate.invoice_ids, nowait=False
            )
            [
                ia.update_invoice_charge_item_details(
                    ic.invoice_charge_id,
                    room_stay_id_to_charge_id_map.get(ic.charge_id),
                )
                for ia in invoice_aggregates
                for ic in ia.invoice_charges
                if not ic.charge_item.details.get('room_stay_id')
                and room_stay_id_to_charge_id_map.get(ic.charge_id)
            ]
            (
                invoice_aggregates,
                hotel_invoices,
            ) = self.invoice_confirmation_service.confirm_invoices(
                invoice_aggregates, bill_aggregate, booking_aggregate, hotel_context
            )

            if hotel_invoices:
                self.invoice_repository.save_all(hotel_invoices)

            if invoice_aggregates:
                self.invoice_repository.update_all(invoice_aggregates)
                updated_aggregates['invoice_aggregates'] = invoice_aggregates
                einvoice_failure = bool(
                    [
                        i
                        for i in invoice_aggregates
                        if i.invoice.is_einvoice and not i.invoice.irn
                    ]
                )
                self.job_scheduler_service.schedule_invoice_upload(
                    bill_aggregate,
                    invoice_aggregates,
                    send_invoices_to_guest=not einvoice_failure,
                )

            booking_invoice_group_aggregate.update_status(InvoiceGroupStatus.GENERATED)

            self.booking_invoice_group_repository.update(
                booking_invoice_group_aggregate
            )

        privileges = crs_context.privileges_as_dict
        # add attachments to booking relocation
        if (
            checkout_action_payload.get('attachment_details')
            and user_data.user_auth_id
            and user_data.application
        ):
            if (
                privileges
                and PrivilegeCode.EMAIL_ATTACHMENT_FOR_RELOCATED_BOOKING in privileges
            ):
                attachment_details = checkout_action_payload['attachment_details']
                attachment_details[
                    'attachment_group'
                ] = AttachmentGroup.BOOKING_RELOCATION_REQUEST
                attachment = self.attachment_service.add_attachment(
                    booking_aggregate.booking_id,
                    [attachment_details],
                    user_data.application,
                    user_data,
                    status=AttachmentStatus.VERIFIED,
                )[0]

        target_booking_aggregate = None
        if checkout_action_payload.get('booking_relocation_details'):
            target_booking_aggregate = self._relocate_booking(checkout_action_payload)

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stay_from_charge_ids(
                bill_aggregate, booking_aggregate
            )
            if booking_aggregate.booking.status == BookingStatus.CHECKED_OUT:
                booking_aggregate.lock_all_ta_commission()

        """
            Treebo's Use Case:
            In case of FDM, only the Payment at Hotel (PAH) amount is refundable.
            Additionally, communication is sent to the guest regarding the remaining amount.
        """
        if checkout_action_payload.get('refund_details'):
            if (
                privileges
                and PrivilegeCode.CAN_ONLY_REFUND_PAH_AMOUNT in privileges
                and checkout_action_payload['refund_details']['total_ptt_amount'].amount
                > 0
            ):
                self.job_scheduler_service.schedule_partial_refund_job_communication(
                    hotel_id=booking_aggregate.booking.hotel_id,
                    reference_number=booking_aggregate.booking.reference_number,
                    refund_details=checkout_action_payload['refund_details'],
                    email=booking_aggregate.get_receiver_email_for_invoice(),
                    phone=booking_aggregate.get_receiver_phone_number_for_invoice(),
                    name=booking_aggregate.get_booking_owner().name.full_name,
                )

        self.booking_repository.update(booking_aggregate)
        if target_booking_aggregate:
            self.booking_repository.update(target_booking_aggregate)
        self.booking_action_repository.save(booking_action_aggregate)
        updated_aggregates['booking_aggregate'] = booking_aggregate

        self.bill_repository.update(bill_aggregate)
        updated_aggregates['bill_aggregate'] = bill_aggregate

        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="checkout"
        )

        locked_invoice_aggregates = updated_aggregates.get('invoice_aggregates') or []
        locked_invoice_aggregates = [
            inv
            for inv in locked_invoice_aggregates
            if inv.status == InvoiceStatus.LOCKED
        ]

        if locked_invoice_aggregates:
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_UPDATED,
                invoice_aggregates=locked_invoice_aggregates,
                user_action="lock_invoice_for_booking",
            )
        if booking_aggregate.booking.status == BookingStatus.CHECKED_OUT:
            if crs_context.is_treebo_tenant():
                self._trigger_checkout_segment_event(
                    booking_aggregate=booking_aggregate,
                    bill_aggregate=bill_aggregate,
                    hotel_aggregate=hotel_aggregate,
                )

            if (
                self.tenant_settings.should_email_invoice_on_checkout(
                    hotel_id=booking_aggregate.booking.hotel_id
                )
                and booking_invoice_group_aggregate.is_last_checkout
                and booking_invoice_group_aggregate.invoice_ids
            ):
                invoice_ids = [
                    invoice_aggregate.invoice_id
                    for invoice_aggregate in updated_aggregates.get(
                        'invoice_aggregates'
                    )
                    if not self._should_block_invoice_communication(
                        invoice_aggregate, booking_aggregate
                    )
                ]
                if invoice_ids:
                    self.job_scheduler_service.schedule_invoice_email_on_checkout(
                        hotel_id=booking_aggregate.booking.hotel_id,
                        booking_id=booking_aggregate.booking.booking_id,
                        invoice_ids=invoice_ids,
                    )

        return booking_action_aggregate

    def _schedule_lco_full_fill_job(
        self,
        booking_aggregate,
        checkout_request_per_rooms,
    ):
        lco_services = []
        for checkout_request in checkout_request_per_rooms:
            room_stay = booking_aggregate.get_room_stay(checkout_request.room_stay_id)
            if room_stay.status == BookingStatus.CHECKED_OUT:
                lco_services.extend(
                    booking_aggregate.get_active_expenses_for_service_type(
                        room_stay.room_stay_id,
                        ServiceTypes.LATE_CHECKOUT,
                    )
                )

        lco_block_ids_to_full_fill = []
        for service in lco_services:
            if service.service_context and service.service_context.service_details:
                service_details = service.service_context.service_details
                lco_block_ids_to_full_fill.append(service_details.inventory_block_id)

        if not lco_block_ids_to_full_fill:
            return

        self.job_scheduler_service.schedule_virtual_inventory_block_manager_job(
            hotel_id=booking_aggregate.hotel_id,
            user_action='checkout',
            block_ids=lco_block_ids_to_full_fill,
        )

    @staticmethod
    def _should_block_invoice_communication(invoice_aggregate, booking_aggregate):
        room_stay_ids = invoice_aggregate.get_linked_room_stay_ids()
        return any(
            booking_aggregate.should_suppress_room_rate(room_stay_id)
            for room_stay_id in room_stay_ids
        )

    def _perform_checkout(
        self, booking_invoice_group_aggregate, booking_aggregate, hotel_aggregate
    ):
        preview_request_time = booking_invoice_group_aggregate.request_datetime
        # TODO: Add validation that checkout datetime cannot be outside business date
        checkout_request_per_rooms = (
            BookingCheckoutRequestGenerator.generate_checkout_request(
                booking_invoice_group_aggregate.room_wise_invoice_request,
                preview_request_time,
                booking_aggregate,
            )
        )

        # NOTE: The below code for overriding checkout time is required in case of past checkout reversal,
        # after another checkin has happened in that room
        # Get max allowed end time for each room, and then update the guest checkout request, if any checkout time is
        #  going beyond max value

        room_wise_room_allotments = {}
        for checkout_request in checkout_request_per_rooms:
            if checkout_request.room_id not in room_wise_room_allotments:
                room_wise_room_allotments[checkout_request.room_id] = [
                    checkout_request.room_allotment_id
                ]
            else:
                room_wise_room_allotments[checkout_request.room_id].append(
                    checkout_request.room_allotment_id
                )

        max_allowed_end_times = (
            self.inventory_application_service.get_max_allowed_end_time_for_allotments(
                hotel_aggregate.hotel_id,
                room_wise_room_allotments,
            )
        )

        BookingCheckoutRequestGenerator.override_checkout_time_if_beyond_max_allowed_end_time(
            checkout_request_per_rooms, max_allowed_end_times
        )

        booking_aggregate.checkout_guests(checkout_request_per_rooms)

        return checkout_request_per_rooms

    def _handle_unused_charges_after_checkout(
        self,
        checkout_request_per_rooms,
        booking_aggregate,
        bill_aggregate,
        posted_charges,
        uninvoiced_charges,
        checkout_datetime,
    ):
        cancelled_charge_ids = set()
        expense_charge_map = booking_aggregate.get_expense_per_room_stay_map(
            include_cancel_no_show_charges=True
        )
        service_cleanup_side_effects = ExpenseServiceCleanupSideEffects()

        grouped_cancelled_charges = GroupedCancelledChargesDto()
        for checkout_request in checkout_request_per_rooms:
            # reassign charges for guests
            guest_ids = {
                guest_checkout_request.guest_id
                for guest_checkout_request in checkout_request.guest_checkout_request_data
            }

            # handle complete room checkout flow
            room_stay = booking_aggregate.get_room_stay(checkout_request.room_stay_id)
            if room_stay.status != BookingStatus.CHECKED_OUT:
                continue

            # cancel unused charges
            unused_charges = room_stay.charge_ids.copy()
            unused_expense_charges = (
                expense_charge_map.get(room_stay.room_stay_id)
                if expense_charge_map.get(room_stay.room_stay_id)
                else []
            )

            billed_entity_ids = [
                booking_aggregate.get_customer(guest_id).billed_entity_id
                for guest_id in guest_ids
                if booking_aggregate.get_customer(guest_id).billed_entity_id is not None
            ]
            charge_ids_for_guest_ids = self._get_charge_ids_for_guest_ids(
                bill_aggregate, billed_entity_ids, guest_ids
            )

            if not any(x in unused_charges for x in posted_charges) and (
                any(x in charge_ids_for_guest_ids for x in unused_charges)
                or len(charge_ids_for_guest_ids) == 0
            ):
                future_charge_ids = self._get_future_charge_ids(
                    bill_aggregate, unused_charges, checkout_datetime
                )
                unused_charges = future_charge_ids if future_charge_ids else []

            if posted_charges:
                if not any(x in unused_expense_charges for x in posted_charges) and (
                    any(x in charge_ids_for_guest_ids for x in unused_expense_charges)
                    or len(charge_ids_for_guest_ids) == 0
                ):
                    unused_expense_charges = []

            if unused_expense_charges:
                unused_charges.extend(unused_expense_charges)

            service_cleanup_side_effects += (
                self.remove_room_stay_services_linked_to_charge(
                    booking_aggregate,
                    bill_aggregate,
                    unused_charges,
                )
            )

            cancelled_charges = set(
                bill_aggregate.cancel_unused_charges(unused_charges)
            )
            if cancelled_charges:
                grouped_cancelled_charges.room_stay_charges.append(
                    RoomStayChargesDto(
                        room_stay.room_stay_id,
                        [
                            room_stay.charge_id_map.pop(
                                dateutils.date_to_ymd_str(
                                    bill_aggregate.get_charge(
                                        charge
                                    ).applicable_business_date
                                )
                            )
                            for charge in set(room_stay.charge_ids).intersection(
                                set([c.charge_id for c in cancelled_charges])
                            )
                        ],
                    )
                )

                grouped_cancelled_charges.expenses.extend(
                    [
                        charge.charge_id
                        for charge in cancelled_charges
                        if charge.charge_id
                        not in set(room_stay.charge_ids).intersection(
                            set([c.charge_id for c in cancelled_charges])
                        )
                    ]
                )

            cancelled_charge_ids.update(
                [charge.charge_id for charge in cancelled_charges]
            )

        service_cleanup_side_effects += self.remove_room_stay_services_linked_to_charge(
            booking_aggregate,
            bill_aggregate,
            uninvoiced_charges,
        )

        cancelled_charges = bill_aggregate.cancel_unused_charges(
            [
                charge_id
                for charge_id in uninvoiced_charges
                if bill_aggregate.get_charge(charge_id).status != ChargeStatus.CANCELLED
            ]
        )
        if grouped_cancelled_charges.expenses:
            booking_aggregate.cancel_expense_for_charges(
                grouped_cancelled_charges.expenses
            )
        cancelled_charge_ids.update([charge.charge_id for charge in cancelled_charges])
        return (
            list(cancelled_charge_ids),
            grouped_cancelled_charges,
            service_cleanup_side_effects,
        )

    @staticmethod
    def remove_room_stay_services_linked_to_charge(
        booking_aggregate, bill_aggregate, charge_ids
    ):
        charge_ids = [
            charge_id
            for charge_id in charge_ids
            if bill_aggregate.get_charge(charge_id).is_unused
        ]
        return StayServiceCleanupFacade.clear_services_by_charges(
            booking_aggregate,
            bill_aggregate,
            charge_ids,
            user_action='checkout',
        )

    def _update_inventory_after_checkout(
        self,
        booking_aggregate,
        checkout_request_per_rooms,
        hotel_aggregate,
        hotel_context,
        addon_inventory_blocks_to_remove=None,
        user_action=None,
    ):
        old_room_allocations = dict()
        room_type_inventory_requirement = InventoryRequirement(
            booking_aggregate.hotel_id
        )

        addon_inventory_requirement = (
            InventoryRequirementService.build_inventory_requirement_from_inventory_blocks(
                booking_aggregate.hotel_id,
                addon_inventory_blocks_to_remove,
            )
            if addon_inventory_blocks_to_remove
            else None
        )

        for checkout_request in checkout_request_per_rooms:
            # handle complete room checkout flow
            room_stay = booking_aggregate.get_room_stay(checkout_request.room_stay_id)
            if room_stay.status != BookingStatus.CHECKED_OUT:
                continue

            # calculate inventory update dates
            request_date = hotel_context.current_date()
            actual_checkout_date = dateutils.to_date(room_stay.checkout_date)
            start_date = min(request_date, actual_checkout_date)
            end_date = actual_checkout_date

            current_room_allocation = room_stay.room_allocation
            old_room_allocations[
                current_room_allocation.room_id
            ] = current_room_allocation

            # create payload for virtual inventory update
            room_type_inventory_requirement.add(
                room_stay.room_type_id, start_date, end_date
            )

        if addon_inventory_requirement:
            room_type_inventory_requirement.merge(addon_inventory_requirement)

        # handle physical inventory update
        for room_id, room_allocation in old_room_allocations.items():
            allotment_end_times = {
                room_allocation.room_allotment_id: room_allocation.checkout_date
            }
            logger.info("Allotment End Times: %s", allotment_end_times)
            self.inventory_application_service.set_room_allotment_actual_end_time(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_id=room_id,
                allotment_id=room_allocation.room_allotment_id,
                actual_end_time=room_allocation.checkout_date,
            )

        # Room type inventory update
        if len(room_type_inventory_requirement) > 0:
            self._release_inventory(
                room_type_inventory_requirement,
                booking_aggregate,
                user_action=user_action,
            )

    def _release_inventory(
        self, room_type_inventory_requirement, booking_aggregate, user_action=None
    ):
        self.inventory_application_service.update_inventory(
            release_inventory=room_type_inventory_requirement,
            user_action=user_action,
        )
        self.room_stay_overflow_service.refresh_overflow_recompute_unmarks(
            room_type_inventory_requirement.min_date,
            room_type_inventory_requirement.max_date,
            room_type_inventory_requirement.room_type_ids,
            booking_aggregate,
            user_action=user_action,
        )

    def _trigger_checkout_segment_event(
        self, booking_aggregate, bill_aggregate, hotel_aggregate
    ):
        user_id = "dummy"
        booking_owner = booking_aggregate.get_booking_owner()
        if getattr(booking_owner, "phone") and getattr(booking_owner.phone, "number"):
            user_id = booking_owner.phone.number
        primary_guest = ""
        for customer in booking_aggregate.customers:
            if customer.is_primary and not customer.dummy:
                primary_guest = str(customer.name)
                break

        event_args = dict(
            platform=getattr(request_context, 'application', 'platform_unknown'),
            booking_id=booking_aggregate.booking.reference_number,
            booker_name=str(booking_owner.name),
            guest_name=primary_guest,
            booker_phone_number=str(booking_owner.phone),
            booker_email_id=booking_owner.email,
            hotel_id=hotel_aggregate.hotel_id,
            hotel_name=hotel_aggregate.hotel.name,
            checkin_date=dateutils.date_to_ymd_str(
                booking_aggregate.booking.checkin_date
            ),
            checkout_date=dateutils.date_to_ymd_str(
                booking_aggregate.booking.checkout_date
            ),
            no_of_rooms=len(booking_aggregate.get_active_room_stays()),
            total_amount=float(bill_aggregate.total_posttax_amount().amount),
            booking_channel=booking_aggregate.booking.source.channel_code,
            booking_subchannel=booking_aggregate.booking.source.subchannel_code,
        )
        self.job_scheduler_service.schedule_segment_event(
            user_id, event_args, SegmentEvent.BOOKING_CHECKOUT_EVENT.value
        )

    @staticmethod
    def _get_charge_ids_for_guest_ids(bill_aggregate, billed_entity_ids, guest_ids):
        charge_ids_for_guest_ids = []
        if billed_entity_ids:
            billed_entity_accounts = bill_aggregate.get_billed_entity_accounts(
                list(map(int, billed_entity_ids))
            )
            billed_entity_account_wise_charge_map = (
                bill_aggregate.get_charge_and_split_ids_for_billed_entity_accounts(
                    billed_entity_accounts, charge_status_filter=[ChargeStatus.CREATED]
                )
            )
            charge_ids_for_guest_ids = set(
                flatten_list(
                    [x for x in billed_entity_account_wise_charge_map.values()]
                )
            )
            charge_ids_for_guest_ids.update(
                [
                    charge.charge_id
                    for charge in bill_aggregate.get_active_charges_for_guests(
                        guest_ids
                    )
                ]
            )
        return charge_ids_for_guest_ids

    @staticmethod
    def _get_future_charge_ids(bill_aggregate, unused_charges, checkout_datetime):
        if dateutils.to_date(checkout_datetime) >= dateutils.current_date():
            future_charge_ids = [
                charge.charge_id
                for charge in bill_aggregate.get_charges(unused_charges)
                if dateutils.to_date(charge.applicable_date)
                > dateutils.to_date(checkout_datetime)
            ]
        else:
            future_charge_ids = [
                charge.charge_id
                for charge in bill_aggregate.get_charges(unused_charges)
                if dateutils.datetime_at_given_time(
                    charge.applicable_date, dateutils.to_time(charge.applicable_date)
                )
                > dateutils.datetime_at_given_time(
                    checkout_datetime, dateutils.to_time(checkout_datetime)
                )
            ]
        return future_charge_ids

    @bypass_access_entity_checks
    def _relocate_booking(self, checkout_action_payload):
        target_booking_aggregate, target_hotel_name = None, None
        booking_relocation_details = checkout_action_payload.get(
            'booking_relocation_details', {}
        )
        target_booking_reference_number = booking_relocation_details.get(
            'booking_reference_number'
        )

        if (
            target_booking_reference_number
            == crs_context.current_booking_aggregate.booking.reference_number
        ):
            raise ValidationException(
                "Booking relocation is not possible on the same booking."
            )

        if target_booking_reference_number and booking_relocation_details.get(
            'relocated_to_another_treebo_hotel'
        ):
            target_booking_aggregate = (
                self.booking_repository.load_for_update_by_reference_number(
                    target_booking_reference_number
                )
            )
            target_hotel_aggregate = self.hotel_repository.load(
                target_booking_aggregate.hotel_id
            )
            target_booking_comment = (
                f"Booking relocated from {crs_context.hotel_context.hotel_name}. "
                f"Original Booking Reference Number {crs_context.current_booking_aggregate.booking.reference_number}, "
                f"Reason : {booking_relocation_details.get('reason')}"
            )
            target_booking_aggregate.update_comments(
                target_booking_comment, is_booking_relocated=True
            )
            target_hotel_name = target_hotel_aggregate.hotel.name
        else:
            target_hotel_name = booking_relocation_details.get('hotel_name', '')

        comment = (
            f"Booking relocated to {target_hotel_name}, "
            f"Booking Reference Number {target_booking_reference_number}, "
            f"Reason : {booking_relocation_details.get('reason')}"
        )
        crs_context.current_booking_aggregate.update_comments(
            comment, is_booking_relocated=True
        )
        crs_context.current_booking_aggregate.update_extra_information(
            booking_relocation_details
        )
        return target_booking_aggregate
