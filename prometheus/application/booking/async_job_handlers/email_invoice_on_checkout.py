from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.booking.guest_communication.invoice_mailer import (
    InvoiceMailer,
)
from prometheus.application.decorators import session_manager
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.exceptions import AggregateNotFound


@register_instance(
    dependencies=[
        BookingRepository,
        InvoiceRepository,
        HotelRepository,
        HotelConfigRepository,
        CreditNoteRepository,
        InvoiceMailer,
        TenantSettings,
        SignedUrlGenerator,
    ]
)
class EmailInvoicesAndCreditNotesHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        invoice_repository: InvoiceRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        credit_note_repository: CreditNoteRepository,
        invoice_mailer: InvoiceMailer,
        tenant_settings: TenantSettings,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.booking_repository = booking_repository
        self.invoice_repository = invoice_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.credit_note_repository = credit_note_repository
        self.invoice_mailer = invoice_mailer
        self.tenant_settings = tenant_settings
        self.signed_url_generator = signed_url_generator

    @session_manager(commit=True)
    def email_invoices_and_credit_notes_to_booker(
        self, booking_id, invoice_ids
    ) -> JobResultDto:
        booking_aggregate = self.booking_repository.load(booking_id)

        hotel_aggregate = self.hotel_repository.load(booking_aggregate.booking.hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            booking_aggregate.booking.hotel_id
        )
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )

        if (
            not booking_aggregate.is_b2b_booking()
            and booking_aggregate.booking.source.channel_code
            not in self.tenant_settings.allowed_channels_to_email_invoice_on_checkout(
                hotel_id=booking_aggregate.booking.hotel_id
            )
        ):
            return JobResultDto.success()

        if booking_aggregate.is_b2b_booking() and not booking_aggregate.is_su_booking():
            return JobResultDto.success()

        invoice_aggregates = self.invoice_repository.load_all(invoice_ids)
        if not invoice_aggregates:
            raise AggregateNotFound(class_name="Invoice", id=invoice_ids)

        for invoice_aggregate in invoice_aggregates:
            signed_url, expiration = self.signed_url_generator.generate_signed_url(
                invoice_aggregate.invoice.invoice_url
            )
            invoice_aggregate.set_signed_url(signed_url, expiration)

        confirmed_invoices = [
            invoice_aggregate
            for invoice_aggregate in invoice_aggregates
            if invoice_aggregate.status != InvoiceStatus.PREVIEW
        ]

        credit_note_aggregates = self.credit_note_repository.load_for_bill_id(
            booking_aggregate.bill_id
        )

        for credit_note_aggregate in credit_note_aggregates:
            signed_url, expiration = self.signed_url_generator.generate_signed_url(
                credit_note_aggregate.credit_note.credit_note_url
            )
            credit_note_aggregate.set_signed_url(signed_url, expiration)

        credit_notes = (
            [
                credit_note_aggregate
                for credit_note_aggregate in credit_note_aggregates
                if not credit_note_aggregate.credit_note.is_cancelled
            ]
            if credit_note_aggregates
            else None
        )

        self.invoice_mailer.send_invoices_to_guest(
            confirmed_invoices, booking_aggregate, credit_notes
        )
        return JobResultDto.success()
