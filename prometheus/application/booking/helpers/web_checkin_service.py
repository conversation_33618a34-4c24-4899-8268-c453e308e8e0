import datetime as dt

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.services.communication_application_service import (
    CommunicationApplicationService,
)
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.core.globals import crs_context
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)
from prometheus.domain.booking.services.web_checkin_domain_service import (
    WebCheckInDomainService,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.scheduled_job_constants import JobName
from ths_common.constants.web_checkin_constants import (
    EmailTemplates,
    MessageTemplates,
    WebCheckinRejectionReason,
    WebCheckinStatus,
)


@register_instance(
    dependencies=[
        WebCheckinRepository,
        BookingRepository,
        AttachmentRepository,
        CommunicationApplicationService,
        JobRegistry,
        JobSchedulerService,
        HotelRepository,
        HotelConfigRepository,
        WebCheckInDomainService,
    ]
)
class WebCheckinService(object):
    def __init__(
        self,
        web_checkin_repository,
        booking_repository,
        attachment_repository,
        communication_application_service,
        job_registry,
        job_scheduler_service,
        hotel_repository,
        hotel_config_repository,
        web_checkin_domain_service,
    ):
        self.web_checkin_repository = web_checkin_repository
        self.booking_repository = booking_repository
        self.attachment_repository = attachment_repository
        self.communication_application_service = communication_application_service
        self.job_scheduler_service = job_scheduler_service
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.web_checkin_domain_service = web_checkin_domain_service
        job_registry.register(
            JobName.WEB_CHECKIN_COMMUNICATION.value, self.send_web_checkin_communication
        )
        job_registry.register(
            JobName.WEB_CHECKIN_REMINDER.value, self.send_web_checkin_reminder
        )
        job_registry.register(
            JobName.WEB_CHECKIN_REJECTION_COMMUNICATION.value,
            self.send_web_checkin_rejection_communication,
        )

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )

    def mark_web_checkin_rejected(self, booking_id, attachment_aggregate):
        web_checkin_aggregates = self.web_checkin_repository.load_all(
            booking_id=booking_id
        )
        if not web_checkin_aggregates:
            return
        booking_aggregate = self.booking_repository.load(
            booking_id=booking_id, skip_expenses=True
        )
        customer = booking_aggregate.get_customer_from_attachment_id(
            attachment_aggregate.attachment.attachment_id
        )
        if not customer:
            return
        is_rejected_notification_rqd = False
        for web_checkin_aggregate in web_checkin_aggregates:
            if web_checkin_aggregate.is_guest_id_present(guest_id=customer.customer_id):
                is_rejected_notification_rqd = True
                web_checkin_aggregate.update_status(status=WebCheckinStatus.REJECTED)

        if is_rejected_notification_rqd:
            self.job_scheduler_service.schedule_web_checkin_rejection_notification(
                booking_id=booking_id,
                hotel_id=booking_aggregate.hotel_id,
                rejection_reason=attachment_aggregate.attachment.rejection_reason,
            )

        self.web_checkin_repository.update_all(web_checkin_aggregates)

    def reject_completed_web_checkins(self, booking_id):
        web_checkin_aggregates = self.web_checkin_repository.load_all(
            booking_id=booking_id
        )
        booking_aggregate = self.booking_repository.load(
            booking_id=booking_id, skip_expenses=True
        )
        if not web_checkin_aggregates:
            return

        is_rejected_notification_rqd = False
        for web_checkin_aggregate in web_checkin_aggregates:
            if web_checkin_aggregate.web_checkin.status != WebCheckinStatus.REJECTED:
                is_rejected_notification_rqd = True
                web_checkin_aggregate.update_status(status=WebCheckinStatus.REJECTED)

        if is_rejected_notification_rqd:
            self.job_scheduler_service.schedule_web_checkin_rejection_notification(
                booking_id=booking_id,
                hotel_id=booking_aggregate.hotel_id,
                rejection_reason=WebCheckinRejectionReason.BOOKING_MODIFICATIONS.reason,
            )

        self.web_checkin_repository.update_all(web_checkin_aggregates)

    def send_web_checkin_communication(self, booking_id) -> JobResultDto:
        booking_aggregate = self.booking_repository.load(booking_id)
        self._set_hotel_context(hotel_id=booking_aggregate.booking.hotel_id)
        web_checkin_aggregates = self.web_checkin_repository.load_all(
            booking_id=booking_id
        )

        if not (web_checkin_aggregates or booking_aggregate.is_cancelled()):
            checkin_minus_two_days = dateutils.datetime_at_given_time(
                dateutils.subtract(
                    date_=booking_aggregate.booking.checkin_date.date(), days=2
                ),
                dt.datetime.strptime('1200', '%H%M').time(),
            )
            sms_template, email_template = (
                MessageTemplates.WEB_CHECKIN_FUTURE_BOOKING,
                None,
            )
            if dateutils.current_datetime() > checkin_minus_two_days:
                sms_template, email_template = (
                    MessageTemplates.WEB_CHECKIN_COMMUNICATION,
                    EmailTemplates.WEB_CHECKIN_REMINDER,
                )
            self.communication_application_service.send_communication_for_web_checkin(
                booking_aggregate=booking_aggregate,
                sms_template=sms_template,
                email_template=email_template,
            )
        return JobResultDto.success()

    def send_web_checkin_reminder(self, booking_id) -> JobResultDto:
        booking_aggregate = self.booking_repository.load(booking_id)
        self._set_hotel_context(hotel_id=booking_aggregate.booking.hotel_id)
        web_checkin_aggregates = self.web_checkin_repository.load_all(
            booking_id=booking_id
        )
        if not (web_checkin_aggregates or booking_aggregate.is_cancelled()):
            self.communication_application_service.send_communication_for_web_checkin(
                booking_aggregate=booking_aggregate,
                email_template=EmailTemplates.WEB_CHECKIN_REMINDER,
                sms_template=MessageTemplates.WEB_CHECKIN_REMINDER,
            )
        return JobResultDto.success()

    def send_web_checkin_rejection_communication(
        self, booking_id, rejection_reason
    ) -> JobResultDto:
        booking_aggregate = self.booking_repository.load(booking_id)
        self._set_hotel_context(hotel_id=booking_aggregate.booking.hotel_id)
        web_checkin_aggregates = self.web_checkin_repository.load_all(
            booking_id=booking_id
        )
        if any(
            web_checkin_aggregate.web_checkin.status == WebCheckinStatus.REJECTED
            for web_checkin_aggregate in web_checkin_aggregates
        ):
            self.communication_application_service.send_rejection_web_checkin_communication(
                booking_aggregate=booking_aggregate, rejection_reason=rejection_reason
            )
        return JobResultDto.success()
