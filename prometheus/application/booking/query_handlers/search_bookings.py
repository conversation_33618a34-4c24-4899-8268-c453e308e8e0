from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.services.booking_search_executor import (
    BookingSearchExecutor,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import (
    BookingRepository,
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.inventory.repositories import RoomAllotmentRepository
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.inventory_constants import AllottedFor
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import group_list


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        RoomStayOverflowRepository,
        NewrelicServiceClient,
        BookingSearchExecutor,
        RoomAllotmentRepository,
    ]
)
class SearchBookingQueryHandler:
    def __init__(
        self,
        booking_repo,
        hotel_repo,
        room_stay_overflow_repo,
        alerting_service,
        booking_search_executor: BookingSearchExecutor,
        room_allotment_repository: RoomAllotmentRepository,
    ):
        self.booking_repository = booking_repo
        self.hotel_repository = hotel_repo
        self.room_stay_overflow_repository = room_stay_overflow_repo
        self.alerting_service = alerting_service
        self.booking_search_executor: BookingSearchExecutor = booking_search_executor
        self.room_allotment_repository: RoomAllotmentRepository = (
            room_allotment_repository
        )

    def handle(self, search_query: BookingSearchQuery, user_data, only_count=False):
        if only_count:
            return self.count_bookings(search_query)

        self.alerting_service.record_event(
            "booking_search", {"hotel_id": search_query.hotel_id}
        )
        response = []
        hotel_id = search_query.hotel_id

        if search_query.checkin_guest_expected_today:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_booking_with_today_expected_checkin(
                    hotel_id,
                    search_query.limit,
                    search_query.offset,
                    user_data=user_data,
                )
            )

        elif search_query.checkout_guest_expected_today:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_booking_with_today_expected_checkout(
                    hotel_id,
                    search_query.limit,
                    search_query.offset,
                    user_data=user_data,
                )
            )

        elif search_query.has_pending_web_checkins:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            booking_aggregates = (
                self.booking_repository.load_bookings_with_pending_web_checkins(
                    hotel_id, search_query
                )
            )

        elif search_query.room_no:
            if search_query.checkin_lte and search_query.checkout_gte:
                date_diff = (search_query.checkin_lte - search_query.checkout_gte).days
                if date_diff > 7:
                    raise ValidationException(
                        ApplicationErrors.DATE_RANGE_EXCEEDS_LIMIT
                    )

                room_allotments = self.room_allotment_repository.load_room_allotment_between_date_range(
                    hotel_id,
                    search_query.room_no,
                    search_query.checkout_gte,
                    search_query.checkin_lte,
                )
                room_allotments = self._filter_room_allotments_active_during_period(
                    room_allotments,
                    search_query.checkout_gte,
                )
            else:
                room_allotment = (
                    self.room_allotment_repository.load_checked_in_room_allotment(
                        hotel_id, search_query.room_no
                    )
                )

                room_allotments = [room_allotment] if room_allotment else []

            booking_aggregates = []
            for room_allotment in room_allotments:
                booking_aggregates.append(
                    self.booking_repository.load_booking_for_room_allocation_id(
                        room_allotment.allotment_id
                    )
                )

        else:
            booking_aggregates = self.booking_search_executor.execute(
                search_query, user_data=user_data
            )

        if not booking_aggregates:
            return response

        hotel_ids = {
            booking_aggregate.booking.hotel_id
            for booking_aggregate in booking_aggregates
        }
        if len(hotel_ids) == 1:
            hotel_id = booking_aggregates[0].booking.hotel_id
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            crs_context.set_hotel_context(hotel_aggregate)
            single_hotel_search = True
        else:
            single_hotel_search = False

        room_stay_overflow_aggregates = (
            self.room_stay_overflow_repository.get_overflowed_room_stays_for_bookings(
                [aggregate.booking_id for aggregate in booking_aggregates]
            )
        )
        grouped_room_stay_overflows = group_list(
            [
                aggregate.room_stay_overflow
                for aggregate in room_stay_overflow_aggregates
            ],
            'booking_id',
        )

        for booking_aggregate in booking_aggregates:
            room_stay_overflows = grouped_room_stay_overflows.get(
                booking_aggregate.booking_id, []
            )
            overflowed_room_stay_ids = [
                overflow.room_stay_id for overflow in room_stay_overflows
            ]
            booking_aggregate.tag_room_stay_overflows(overflowed_room_stay_ids)
            if single_hotel_search:
                booking_aggregate.refresh_allowed_actions()
            response.append(booking_aggregate)
        return response

    def count_bookings(self, search_query: BookingSearchQuery):
        hotel_id = search_query.hotel_id
        if search_query.checkin_guest_expected_today:
            count = self.booking_repository.count_booking_with_today_expected_checkin(
                hotel_id
            )
        elif search_query.checkout_guest_expected_today:
            count = self.booking_repository.count_booking_with_today_expected_checkout(
                hotel_id
            )
        elif search_query.has_pending_web_checkins:
            count = self.booking_repository.count_bookings_with_pending_web_checkins(
                hotel_id
            )
        else:
            count = self.booking_search_executor.execute(search_query, count_only=True)
        return count

    @staticmethod
    def _filter_room_allotments_active_during_period(room_allotments, start_time):
        filtered_room_allotments = []
        for room_allotment in room_allotments:
            if (
                room_allotment.allotted_for == AllottedFor.FUTURE_STAY.value
                and not room_allotment.deleted
            ):
                filtered_room_allotments.append(room_allotment)
            elif room_allotment.allotted_for == AllottedFor.STAY.value:
                if (
                    room_allotment.actual_end_time
                    and room_allotment.actual_end_time < start_time
                ):
                    continue
                filtered_room_allotments.append(room_allotment)

        return filtered_room_allotments
