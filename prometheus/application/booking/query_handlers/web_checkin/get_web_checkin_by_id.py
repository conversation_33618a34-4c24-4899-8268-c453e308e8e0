from object_registry import register_instance
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)


@register_instance(dependencies=[WebCheckinRepository])
class GetWebCheckinByIdQueryHandler:
    def __init__(self, web_checkin_repository: WebCheckinRepository):
        self.web_checkin_repository = web_checkin_repository

    def handle(self, booking_id, web_checkin_id):
        return self.web_checkin_repository.load(
            booking_id=booking_id, web_checkin_id=web_checkin_id
        )
