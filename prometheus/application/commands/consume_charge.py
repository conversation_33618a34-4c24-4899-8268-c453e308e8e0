import logging

from treebo_commons.utils import dateutils

from prometheus.application.helpers.billed_entity_helper import (
    get_room_stay_default_billed_entity,
)
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from ths_common.constants.billing_constants import (
    BillAppId,
    ChargeSplitType,
    ChargeStatus,
    ChargeTypes,
    ExpenseItemIds,
)
from ths_common.constants.booking_constants import AgeGroup
from ths_common.exceptions import ValidationException
from ths_common.utils.collectionutils import flatten_list
from ths_common.value_objects import Occupancy, RoomChargeItemDetails

logger = logging.getLogger(__name__)


class ConsumeChargeCommand(object):
    def __init__(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        business_date,
        hotel_context,
        room_type_name_map,
        is_checkout=False,
        exclude_charge_ids=None,
        checkout_date=None,
        allowed_charge_status=None,
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.business_date = business_date
        self.room_type_name_map = room_type_name_map
        self.is_checkout = is_checkout
        self.exclude_charge_ids = exclude_charge_ids
        self.checkout_date = checkout_date
        self.allowed_charge_status = (
            allowed_charge_status
            if allowed_charge_status
            else [ChargeStatus.CREATED, ChargeStatus.PREVIEW]
        )

    def execute(self, charge_ids=None):
        # in case of early checkout all the charges till checkout time should be consumed
        # see definition checkout datetime preview_invoice_helpers.get_checkout_datetime
        to_date_for_charge_consumption = self.checkout_date or self.business_date
        if self.bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
            charge_ids_to_consume = None
            if charge_ids:
                # Only these charges will be marked consumed
                charges_to_consume = self.bill_aggregate.filter_and_get_charges(
                    charge_ids
                )
                addon_charge_ids = flatten_list(
                    [
                        c.addon_charge_ids
                        for c in charges_to_consume
                        if c.addon_charge_ids
                    ]
                )
                addon_charges = self.bill_aggregate.filter_and_get_charges(
                    addon_charge_ids
                )
                charges_to_consume.extend(addon_charges)

                charge_ids_to_consume = [c.charge_id for c in charges_to_consume]

            filtered_charge_map = (
                self.booking_aggregate.get_applicable_charges_till_date(
                    to_date_for_charge_consumption, self.is_checkout
                )
            )
            all_filtered_charges = []

            # get the charges which are not mapped to a room separately
            charge_ids_not_mapped_to_rooms = filtered_charge_map.pop('default', None)
            if charge_ids_not_mapped_to_rooms:
                if charge_ids_to_consume:
                    all_filtered_charges.extend(
                        self.bill_aggregate.filter_and_get_charges(
                            [
                                cid
                                for cid in charge_ids_not_mapped_to_rooms
                                if cid in charge_ids_to_consume
                            ],
                            allowed_charge_status=self.allowed_charge_status,
                        )
                    )
                else:
                    all_filtered_charges.extend(
                        self.bill_aggregate.filter_and_get_charges(
                            charge_ids=charge_ids_not_mapped_to_rooms,
                            allowed_charge_status=self.allowed_charge_status,
                        )
                    )

            # update room info and split info in the charges
            for room_stay_id, charge_ids in filtered_charge_map.items():
                room_stay = self.booking_aggregate.get_room_stay(room_stay_id)
                if charge_ids_to_consume:
                    charge_ids = [
                        cid for cid in charge_ids if cid in charge_ids_to_consume
                    ]

                filtered_charges = self.bill_aggregate.filter_and_get_charges(
                    charge_ids=charge_ids,
                    allowed_charge_status=self.allowed_charge_status,
                )
                charge_ids = [charge.charge_id for charge in filtered_charges]
                if self.exclude_charge_ids:
                    filtered_charges = [
                        charge
                        for charge in filtered_charges
                        if charge.charge_id not in self.exclude_charge_ids
                    ]

                filtered_charges_without_room_stay = [
                    charge
                    for charge in filtered_charges
                    if charge.item.item_id
                    not in (ExpenseItemIds.NO_SHOW, ExpenseItemIds.BOOKING_CANCELLATION)
                ]

                if filtered_charges_without_room_stay:
                    self._update_charge_details(
                        filtered_charges_without_room_stay,
                        room_stay,
                        self.room_type_name_map,
                        self.booking_aggregate.booking_id,
                    )

                all_filtered_charges.extend(filtered_charges)
        else:
            all_filtered_charges = self.bill_aggregate.filter_and_get_charges(
                charge_ids
            )

        self._fail_if_charge_belongs_to_non_checked_in_room_stay(
            charge_ids, all_filtered_charges
        )

        # posting date should be always business date
        self.bill_aggregate.consume_charges(all_filtered_charges, self.business_date)

        return all_filtered_charges

    def _update_charge_details(
        self, filtered_charges, room_stay, room_type_name_map, booking_id
    ):
        date_wise_occupancies = room_stay.date_wise_occupancies
        room_no = (
            room_stay.room_allocation.room_no if room_stay.room_allocation else None
        )
        room_stay_id = room_stay.room_stay_id
        room_type = room_type_name_map[room_stay.room_type_id].room_type.type
        charged_entity_id = '{b}-{r:02}'.format(b=booking_id, r=room_stay.room_stay_id)

        default_billed_entity = get_room_stay_default_billed_entity(
            room_stay, self.booking_aggregate, self.bill_aggregate
        )
        # Add charge split and room info
        for charge in filtered_charges:
            charge_date = dateutils.to_date(charge.applicable_date)
            checked_in_guests = room_stay.checked_in_guest_ids_on_date(
                charge_date, age_group=AgeGroup.ADULT
            )
            self.bill_aggregate.update_consuming_guests(charge, checked_in_guests)
            if not charge.charge_splits:
                # TODO: Call shouldn't come here. A charge going forward should always be created with ChargeSplit
                charge.update_charge_splits(
                    [
                        ChargeSplitData(
                            self.bill_aggregate.get_billed_entity_account_for_new_assignment(
                                default_billed_entity,
                                charge.type or ChargeTypes.NON_CREDIT,
                            ),
                            charge_type=charge.type or ChargeTypes.NON_CREDIT,
                        )
                    ],
                    ChargeSplitType.EQUAL_SPLIT,
                )
            # add room info
            charge_item_details = RoomChargeItemDetails.from_dict(charge.item.details)
            charge_item_details.update_room_stay_id(room_stay_id)
            charge_item_details.update_room_no(room_no)
            charge_item_details.update_room_type(room_type)
            charge_item_details.update_charged_entity_id(charged_entity_id)

            occupancy = date_wise_occupancies.get(charge_date)
            if not occupancy:
                # If charge is on checkout date, then occupancy on last date won't be found. Use date - 1, for occupancy
                occupancy = date_wise_occupancies.get(
                    dateutils.subtract(charge_date, days=1), Occupancy(0)
                )
            charge_item_details.update_occupancy(occupancy.adult)
            self.bill_aggregate.update_charge_item_details(charge, charge_item_details)

    def _fail_if_charge_belongs_to_non_checked_in_room_stay(
        self, charge_ids, all_filtered_charges
    ):
        if self.bill_aggregate.is_pos_bill():
            return

        room_stays_charges_map = (
            self.booking_aggregate.get_all_applicable_charges_on_room_stays_map()
        )
        charge_ids = (
            charge_ids
            if charge_ids
            else [charge.charge_id for charge in all_filtered_charges]
        )
        for charge_id in charge_ids:
            for room_stay, applicable_charge_ids in room_stays_charges_map:
                if (
                    not room_stay.is_checkin_performed()
                    and charge_id in applicable_charge_ids
                ):
                    raise ValidationException(
                        BillingErrors.CHARGE_CANNOT_BE_CONSUMED_BEFORE_CHECK_IN
                    )
