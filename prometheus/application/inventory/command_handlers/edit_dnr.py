import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit_dnr, audit_housekeeping
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.services.dnr_service import DNRService
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.dnr_facts import DNRFacts
from ths_common.constants.audit_trail_constants import DNRAuditType
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DNRRepository,
        RoomAllotmentRepository,
        RoomRepository,
        RoomTypeInventoryRepository,
        DNRService,
    ]
)
class EditDNRCommandHandler:
    def __init__(
        self,
        dnr_repository: DNRRepository,
        room_allotment_repository: RoomAllotmentRepository,
        room_repository: RoomRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        dnr_service: DNRService,
    ):
        self.dnr_repository = dnr_repository
        self.room_allotment_repository = room_allotment_repository
        self.room_repository = room_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.dnr_service = dnr_service

    @session_manager(commit=True)
    @audit_dnr(audit_type=DNRAuditType.DNR_MODIFIED)
    @audit_housekeeping(action_performed="edit_dnr")
    def handle(self, hotel_id, dnr_id, resource_version, dnr_data, user_data):
        crs_context_middleware.set_hotel_context(hotel_id)
        hotel_context = crs_context.get_hotel_context()

        dnr_aggregate = self.dnr_repository.load_dnr_for_update(
            hotel_id, dnr_id, resource_version
        )
        if dnr_aggregate.dnr.is_inactive():
            raise ValidationException(ApplicationErrors.CANT_EDIT_INACTIVE_DNR)

        RuleEngine.action_allowed(
            action='edit_dnr',
            facts=DNRFacts(
                dnr_aggregate.dnr,
                user_type=user_data.user_type,
                action_payload=dnr_data,
                hotel_context=hotel_context,
            ),
            fail_on_error=True,
        )

        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_id, room_id=dnr_aggregate.dnr.room_id
        )
        room_aggregate = self.room_repository.load(
            room_id=dnr_aggregate.dnr.room_id, hotel_id=hotel_id
        )

        current_from_date = dnr_aggregate.dnr.start_date
        current_end_date = dnr_aggregate.dnr.end_date
        new_from_date = (
            dnr_data.get('from_date') if 'from_date' in dnr_data else current_from_date
        )
        new_end_date = (
            dnr_data.get('to_date') if 'to_date' in dnr_data else current_end_date
        )

        if (
            dnr_data.get('from_date')
            and dnr_data.get('from_date') != current_from_date
            and current_from_date < hotel_context.current_date()
        ):
            raise ValidationException(
                ApplicationErrors.CANT_EDIT_START_DATE_OF_DNR_STARTING_IN_PAST
            )

        if (
            dnr_data.get('to_date')
            and dnr_data.get('to_date') != current_end_date
            and current_end_date <= hotel_context.current_date()
        ):
            raise ValidationException(
                ApplicationErrors.CANT_EDIT_END_DATE_OF_INACTIVE_DNR
            )

        if (
            dnr_data.get('to_date')
            and dnr_data.get('to_date')
            <= hotel_context.current_date()
            < current_end_date
        ):
            raise ValidationException(
                ApplicationErrors.CANT_MOVE_DNR_END_DATE_TO_PAST_DATE
            )

        min_from_date = min(current_from_date, new_from_date)
        max_end_date = max(current_end_date, new_end_date)

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_multiple(
                hotel_id=hotel_id,
                room_type_ids=[room_aggregate.room.room_type_id],
                from_date=min_from_date,
                to_date=max_end_date,
            )
        )
        room_type_inventory_aggregate = (
            room_type_inventory_aggregates[0]
            if room_type_inventory_aggregates
            else None
        )

        date_wise_room_type_availability_change = self.dnr_service.edit_dnr(
            dnr_aggregate,
            dnr_data,
            room_type_inventory_aggregate,
            room_allotment_aggregate,
            hotel_context,
        )

        if (
            current_from_date > hotel_context.current_date()
            and new_from_date == hotel_context.current_date()
        ):
            if not room_allotment_aggregate.is_occupied():
                room_allotment_aggregate.update_housekeeping_status_on_dnr()

        if (
            current_from_date == hotel_context.current_date()
            and new_from_date > hotel_context.current_date()
        ):
            if not room_allotment_aggregate.is_occupied():
                room_allotment_aggregate.update_housekeeping_status_on_dnr_resolution()

        self.room_allotment_repository.update(room_allotment_aggregate)
        self.dnr_repository.update(dnr_aggregate)
        self.room_type_inventory_repository.update(room_type_inventory_aggregate)

        IntegrationEventApplicationService.create_dnr_events(
            hotel_id,
            dnr_aggregate,
            {room_aggregate.room.room_type_id: date_wise_room_type_availability_change},
            event_type=IntegrationEventType.DNR_UPDATED,
            user_action="edit_dnr",
        )
        return dnr_aggregate.dnr
