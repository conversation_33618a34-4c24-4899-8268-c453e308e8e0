from object_registry import register_instance
from prometheus.domain.catalog.repositories import HotelRepository, RoomTypeRepository
from prometheus.domain.inventory.repositories import RoomTypeInventoryRepository


@register_instance(
    dependencies=[HotelRepository, RoomTypeRepository, RoomTypeInventoryRepository]
)
class GetAvailabilityQueryHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        room_type_repository: RoomTypeRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
    ):
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.room_type_inventory_repository = room_type_inventory_repository

    def handle(self, hotel_id, from_date, to_date, room_type_id=None):
        self.hotel_repository.load(hotel_id)
        if room_type_id:
            self.room_type_repository.load(room_type_id)
        room_type_inventory_availability_aggregates = (
            self.room_type_inventory_repository.load_multiple(
                hotel_id, from_date, to_date, [room_type_id] if room_type_id else None
            )
        )
        room_type_inventory_availabilities = []
        for (
            room_type_inventory_availability_aggregate
        ) in room_type_inventory_availability_aggregates:
            room_type_inventory_availabilities.extend(
                room_type_inventory_availability_aggregate.room_type_inventory_availabilities
            )
        return room_type_inventory_availabilities
