from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from ths_common.constants.billing_constants import (
    ChargeStatus,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.catalog_constants import SkuCategory


class ArrivalReportAggregate(BaseReportAggregate):
    """
    Each aggregate object is a row in the marvin report
    """

    def __init__(self, booking_aggregate, bill_aggregate, hotel_aggregate, room_stay):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.hotel_context = crs_context.set_hotel_context(hotel_aggregate)
        self.room_stay = room_stay

    @property
    def hotel_id(self):
        return self.hotel_aggregate.hotel.hotel_id

    @property
    def hotel_name(self):
        return self.hotel_aggregate.hotel.name

    def _customer(self):
        guest_stays = self.room_stay.active_guest_stays()
        if guest_stays and guest_stays[0].guest_id:
            return self.booking_aggregate.get_customer(guest_stays[0].guest_id)
        else:
            return self.booking_aggregate.get_booking_owner()

    @property
    def guest_name(self):
        customer = self._customer()
        return ' '.join(filter(None, (customer.first_name, customer.last_name)))

    @property
    def guest_phone(self):
        customer = self._customer()
        return customer.phone

    @property
    def guest_email(self):
        customer = self._customer()
        return customer.email

    @property
    def guest_gender(self):
        customer = self._customer()
        return customer.gender

    @property
    def guest_nationality(self):
        customer = self._customer()
        return customer.nationality

    @property
    def guest_address(self):
        customer = self._customer()
        if customer.address:
            return ' '.join(
                filter(None, (customer.address.field_1, customer.address.field_2))
            )
        return None

    @property
    def guest_state(self):
        customer = self._customer()
        if customer.address:
            return customer.address.state
        return None

    @property
    def guest_city(self):
        customer = self._customer()
        if customer.address:
            return customer.address.city
        return None

    @property
    def guest_country(self):
        customer = self._customer()
        if customer.address:
            return customer.address.country
        return None

    @property
    def guest_pincode(self):
        customer = self._customer()
        if customer.address:
            return customer.address.pincode
        return None

    @property
    def booking_id(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def booking_status(self):
        return self.booking_aggregate.booking.status

    @property
    def bill_id(self):
        return self.bill_aggregate.bill.bill_id

    @property
    def reference_number(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def room_number(self):
        return (
            self.room_stay.room_allocation.room_no
            if self.room_stay.room_allocation
            else None
        )

    @property
    def room_type(self):
        return (
            self.room_stay.room_allocation.room_type_id
            if self.room_stay.room_allocation
            else None
        )

    @property
    def adult_count(self):
        return len(self.room_stay.adult_guest_stays())

    @property
    def child_count(self):
        return len(self.room_stay.all_guest_stays_except_cancelled()) - len(
            self.room_stay.adult_guest_stays()
        )

    @property
    def guest_count(self):
        return self.child_count + self.adult_count

    @property
    def checkin_date(self):
        checkin_date = self.room_stay.actual_checkin_date or self.room_stay.checkin_date
        return self.hotel_context.hotel_checkin_date(checkin_date)

    @property
    def checkout_date(self):
        checkout_date = (
            self.room_stay.actual_checkout_date or self.room_stay.checkout_date
        )
        return self.hotel_context.hotel_checkout_date(checkout_date)

    def get_payments(self):
        return [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]

    @property
    def get_channel(self):
        return self.booking_aggregate.booking.source.channel_id

    @property
    def room_stay_id(self):
        return self.room_stay.room_stay_id

    def get_room_stay_charges(self, room_stay_id=None):
        # TODO: check that all required expenses are fetched
        if room_stay_id is None:
            room_stay_id = self.room_stay_id

        charge_ids = self.booking_aggregate.get_room_stay_charges([room_stay_id])
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def get_room_stay_expenses(self, room_stay_id=None):
        if room_stay_id is None:
            room_stay_id = self.room_stay_id

        charge_ids = [
            expense.charge_id
            for expense in self.booking_aggregate.get_active_expenses_for_room_stay(
                room_stay_id
            )
        ]
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def charges(self):
        charges = self.get_room_stay_charges(self.room_stay_id)
        charges.extend(self.get_room_stay_expenses(self.room_stay_id))
        return charges

    @property
    def date_wise_stay_charges_posttax(self):
        datewise_charge_amount = defaultdict(
            lambda: Money(0, self.bill_aggregate.bill.base_currency)
        )
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                applicble_date = dateutils.date_to_ymd_str(charge.applicable_date)
                datewise_charge_amount[applicble_date] = (
                    datewise_charge_amount[applicble_date]
                    + charge.get_posttax_amount_post_allowance()
                )
        return datewise_charge_amount

    @property
    def date_wise_stay_charges_tax(self):
        datewise_tax_amount = defaultdict(
            lambda: Money(0, self.bill_aggregate.bill.base_currency)
        )
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                applicable_date = dateutils.date_to_ymd_str(charge.applicable_date)
                datewise_tax_amount[applicable_date] = (
                    datewise_tax_amount[applicable_date]
                    + charge.get_tax_amount_post_allowance()
                )
        return datewise_tax_amount

    @property
    def date_wise_stay_charges_pretax(self):
        datewise_pretax_amount = defaultdict(
            lambda: Money(0, self.bill_aggregate.bill.base_currency)
        )
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                applicble_date = dateutils.date_to_ymd_str(charge.applicable_date)
                datewise_pretax_amount[applicble_date] = (
                    datewise_pretax_amount[applicble_date]
                    + charge.get_pretax_amount_post_allowance()
                )
        return datewise_pretax_amount

    @property
    def room_related_charges_posttax(self):
        total_room_charges = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            total_room_charges += charge.get_posttax_amount_post_allowance()
        return total_room_charges

    def room_related_charges_posttax_between_stay_dates(self, date1, date2):
        total_room_charges = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if (
                charge.item.sku_category_id == SkuCategory.STAY.value
                and date1 <= charge.applicable_date.date() <= date2
            ):
                total_room_charges += charge.get_posttax_amount_post_allowance()
        return total_room_charges

    @property
    def room_related_charges_tax(self):
        tax_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                tax_amount += charge.get_tax_amount_post_allowance()
        return tax_amount

    def room_related_charges_tax_for_dates(self, date1, date2):
        tax_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if (
                charge.item.sku_category_id == SkuCategory.STAY.value
                and date1 <= charge.applicable_date.date() <= date2
            ):
                tax_amount += charge.get_tax_amount_post_allowance()
        return tax_amount

    @property
    def room_related_charges_pretax(self):
        pretax_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if charge.item.sku_category_id == SkuCategory.STAY.value:
                pretax_amount += charge.get_pretax_amount_post_allowance()
        return pretax_amount

    def room_related_charges_pretax_for_dates(self, date1, date2):
        pretax_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for charge in self.charges():
            if (
                charge.item.sku_category_id == SkuCategory.STAY.value
                and date1 <= charge.applicable_date.date() <= date2
            ):
                pretax_amount += charge.get_pretax_amount_post_allowance()
        return pretax_amount

    @property
    def payment_amount(self):
        payment_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for payment in self.get_payments():
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_amount += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_amount -= payment.amount
        return payment_amount

    @property
    def pending_amount(self):
        return self.room_related_charges_posttax - self.payment_amount

    @property
    def id_proof_type(self):
        customer = self.booking_aggregate.get_customer(
            self.booking_aggregate.booking.owner_id
        )
        return customer.id_proof.id_proof_type if customer.id_proof else None

    @property
    def id_proof_number(self):
        customer = self.booking_aggregate.get_customer(
            self.booking_aggregate.booking.owner_id
        )
        return customer.id_proof.id_number if customer.id_proof else None
