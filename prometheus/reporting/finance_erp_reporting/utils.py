import json
import logging
import os
from functools import wraps

import requests
from flask import current_app as app

from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationServiceClient,
)
from prometheus.reporting.finance_erp_reporting.constants import (
    CountryCode,
    finance_context,
)

logger = logging.getLogger(__name__)


def to_pascal_case(text):
    return ''.join(word.title() for word in text.split('_'))


def to_finance_erp_room_type(room_type):
    finance_erp_room_type_map = {
        'acacia': 'Acacia(Solo)',
        'mahogany': 'Mahogany(Premium)',
        'maple': 'Maple(Deluxe)',
        'oak': 'Oak(Standard)',
    }
    return finance_erp_room_type_map.get(room_type.lower())


def to_ymd_str(date):
    return date.strftime("%Y-%m-%d")


def sanitize_string(string, length=False, only_alpha=False):
    if not string:
        return string
    if length:
        string = string[0:length]
    if only_alpha:
        string = ''.join([char for char in string if char.isalpha()])
    return string


def get_state_code(state):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(f'{dir_path}/state_code_map.json') as file:
        state_code_map = json.load(file)
        for key, value in state_code_map.items():
            if state.replace(" ", "").lower() in key.replace(" ", "").lower():
                return value


def get_state_code_from_gstin(gstin):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(f'/{dir_path}/gstin_state_code_map.json') as file:
        gstin_state_code_map = json.load(file)
        return gstin_state_code_map[gstin]


def get_country_code(country):
    return CountryCode.INDIA.value if country.lower() in 'INDIA'.lower() else None
