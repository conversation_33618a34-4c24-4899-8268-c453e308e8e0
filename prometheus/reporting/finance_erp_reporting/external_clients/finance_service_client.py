import json
import logging
import os

from flask import current_app as app

from object_registry import locate_instance, register_instance
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_report_aggregate import (
    SettlementReportAggregate,
)
from prometheus.reporting.finance_erp_reporting.serialisers.finance_erp_client import (
    OTACommissionPushSchema,
    PGTransactionPushSchema,
    PurchaseInvoiceDataPushSchema,
    SaleInvoiceDataPushSchema,
    SettlementExpensePushSchema,
    SettlementHotelAdjustmentPushSchema,
    SettlementLoanPushSchema,
    SettlementTaxPushSchema,
    SettlementTreeboFeePushSchema,
    TACommissionPushSchema,
)
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance()
class FinanceServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=4000)
        self.catalog_service_client = locate_instance(CatalogServiceClient)

    page_map = {
        'json_payment_gateway': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/payment-data/ingest-async",
        ),
        'json_sales': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/sales-data/ingest-async",
        ),
        "json_purchase": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/purchase-data/ingest-async",
        ),
        'json_hotel': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/hotel-data/ingest-async",
        ),
        'json_corporate': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/corporate-data/ingest-async",
        ),
        'json_otacommission': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/ota-commission-data/ingest-async",
        ),
        'json_treebofee': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/settlement/treebo-fee-data/ingest-async",
        ),
        'json_taxentry': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/settlement/tax-data/ingest-async",
        ),
        'json_loanentry': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/settlement/loan-data/ingest-async",
        ),
        'json_expenseentry': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/settlement/expense-data/ingest-async",
        ),
        'json_hoteladjust': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/settlement/hotel-adjustment-data/ingest-async",
        ),
        'json_tacommission': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/ta-commission-data/ingest-async",
        ),
        'json_financial_data_sync': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/back-office/financial-data/ingest",
        ),
    }

    def get_domain(self):
        return "http://localhost:8000"
        return ServiceRegistryClient.get_finance_portal_service_url()

    def push_payments_data(self, pg_aggregates):
        json_key = 'json_payment_gateway'
        data_to_push = (
            PGTransactionPushSchema()
            .dump(self.filter_out_test_data(pg_aggregates), many=True)
            .data
        )
        print("Data to push: %s" % data_to_push)
        # self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_sale_invoices(self, customer_invoice_report_aggregates):
        json_key = 'json_sales'
        data_to_push = (
            SaleInvoiceDataPushSchema()
            .dump(
                self.filter_out_test_data(customer_invoice_report_aggregates), many=True
            )
            .data
        )
        self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_purchase_invoices(self, customer_invoice_report_aggregates):
        json_key = 'json_purchase'
        data_to_push = (
            PurchaseInvoiceDataPushSchema()
            .dump(
                self.filter_out_test_data(customer_invoice_report_aggregates), many=True
            )
            .data
        )
        self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_ota_commission_data(self, ota_commission_aggregates):
        json_key = 'json_otacommission'
        data_to_push = (
            OTACommissionPushSchema()
            .dump(self.filter_out_test_data(ota_commission_aggregates), many=True)
            .data
        )
        self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_ta_commission_data(self, ta_commission_aggregates):
        json_key = 'json_tacommission'
        data_to_push = (
            TACommissionPushSchema()
            .dump(self.filter_out_test_data(ta_commission_aggregates), many=True)
            .data
        )
        self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_settlement_reports(
        self, settlement_report_aggregates: [SettlementReportAggregate]
    ):
        treebo_fee_aggregates = self.filter_out_test_data(
            [
                aggregate
                for settlement_report_aggregate in settlement_report_aggregates
                for aggregate in settlement_report_aggregate.treebo_fee_aggregates
                if aggregate.is_non_zero_entry and aggregate.is_order
            ]
        )
        data_to_push = (
            SettlementTreeboFeePushSchema().dump(treebo_fee_aggregates, many=True).data
        )
        self.push_to_finance_portal(data_to_push, json_key='json_treebofee')

        tax_aggregates = self.filter_out_test_data(
            [
                aggregate
                for settlement_report_aggregate in settlement_report_aggregates
                for aggregate in settlement_report_aggregate.tax_aggregates
                if aggregate.is_non_zero_entry
            ]
        )

        data_to_push = SettlementTaxPushSchema().dump(tax_aggregates, many=True).data
        self.push_to_finance_portal(data_to_push, json_key='json_taxentry')

        loan_aggregates = self.filter_out_test_data(
            [
                aggregate
                for settlement_report_aggregate in settlement_report_aggregates
                for aggregate in settlement_report_aggregate.loan_aggregates
                if aggregate.is_non_zero_entry
            ]
        )

        data_to_push = SettlementLoanPushSchema().dump(loan_aggregates, many=True).data
        self.push_to_finance_portal(data_to_push, json_key='json_loanentry')

        expense_aggregates = self.filter_out_test_data(
            [
                aggregate
                for settlement_report_aggregate in settlement_report_aggregates
                for aggregate in settlement_report_aggregate.expense_aggregates
                if aggregate.is_non_zero_entry and aggregate.is_order
            ]
        )

        data_to_push = (
            SettlementExpensePushSchema().dump(expense_aggregates, many=True).data
        )
        self.push_to_finance_portal(data_to_push, json_key='json_expenseentry')

        hotel_adjustment_aggregates = self.filter_out_test_data(
            [
                aggregate
                for settlement_report_aggregate in settlement_report_aggregates
                for aggregate in settlement_report_aggregate.hotel_adjustment_aggregates
                if aggregate.is_non_zero_entry
            ]
        )

        data_to_push = (
            SettlementHotelAdjustmentPushSchema()
            .dump(hotel_adjustment_aggregates, many=True)
            .data
        )
        self.push_to_finance_portal(data_to_push, json_key='json_hoteladjust')

    def push_to_finance_portal(self, data_to_push, json_key):
        if not data_to_push:
            return True

        for chunked_data_index, chunked_data in enumerate(chunks(data_to_push, 1000)):
            logger.info(
                'Data to be sent for %s to Finance portal: %s',
                json_key,
                json.dumps(chunked_data),
            )
            response = self.make_call(json_key, chunked_data)
            logger.info(
                'Finance portal response for %s with data %s',
                json_key,
                response.json_response,
            )
            if not response.is_success():
                raise Exception(
                    "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                        json_key, response.response_code, response.json_response
                    )
                )

    def push_finance_data(self, finance_data):
        json_key = 'json_financial_data_sync'
        response = self.make_call(json_key, finance_data)
        logger.info(
            'Finance portal response for %s with data %s',
            json_key,
            response.json_response,
        )
        if not response.is_success():
            raise Exception(
                "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                    json_key, response.response_code, response.json_response
                )
            )

    def filter_out_test_data(self, aggregates):
        test_hotel_ids = self.catalog_service_client.get_all_test_property_ids()
        return [
            aggregate
            for aggregate in aggregates
            if not hasattr(aggregate, 'hotel_code')
            or getattr(aggregate, 'hotel_code') not in test_hotel_ids
        ]
