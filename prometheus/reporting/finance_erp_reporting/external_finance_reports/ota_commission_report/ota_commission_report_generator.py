from object_registry import locate_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.external_clients.unirate_service_client import (
    UnirateServiceClient,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.ota_commission_report.ota_commission_report_aggregate import (
    OtaCommissionReportAggregate,
)
from ths_common.constants.billing_constants import (
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
)


class OtaCommissionReportGenerator(BaseReportGenerator):
    def __init__(self, ota_commission_booking_aggregates):
        self.booking_aggregates = ota_commission_booking_aggregates
        self.bill_repository = locate_instance(BillRepository)
        self.unirate_service_client = locate_instance(UnirateServiceClient)

    def generate(self):
        bill_aggregates = self.bill_repository.load_all_with_yield_per(
            {booking_aggregate.bill_id for booking_aggregate in self.booking_aggregates}
        )
        bill_map = {
            bill_aggregate.bill_id: bill_aggregate for bill_aggregate in bill_aggregates
        }
        ota_commission_report_aggregates, bookings_to_be_pushed = [], []
        for booking_aggregate in self.booking_aggregates:
            bill_aggregate = bill_map[booking_aggregate.bill_id]
            if booking_aggregate.is_checked_out():
                bookings_to_be_pushed.append(booking_aggregate)
            if (
                not booking_aggregate.is_active_booking()
                and self.is_paid_at_ota(bill_aggregate)
                and bill_aggregate.get_invoiced_cancellation_no_show_charges()
            ):
                bookings_to_be_pushed.append(booking_aggregate)
        for booking_aggregate in bookings_to_be_pushed:
            bill_aggregate = bill_map[booking_aggregate.bill_id]
            ota_commission_report_aggregates.append(
                OtaCommissionReportAggregate(
                    booking_aggregate,
                    bill_aggregate,
                )
            )
        return ota_commission_report_aggregates

    @staticmethod
    def is_paid_at_ota(bill_aggregate):
        return [
            payment
            for payment in bill_aggregate.payments
            if payment.payment_mode == PaymentModes.PAID_AT_OTA
            and payment.paid_to == PaymentReceiverTypes.OTA
            and payment.status != PaymentStatus.CANCELLED
        ]
