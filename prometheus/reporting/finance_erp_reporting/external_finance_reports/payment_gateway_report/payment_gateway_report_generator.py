from object_registry import locate_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.dtos.finance_erp_dtos import BookingDetailsDto
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.constants import finance_context
from prometheus.reporting.finance_erp_reporting.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.payment_gateway_report.payment_gateway_data_dto import (
    PaymentGatewayDataDto,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.payment_gateway_report.payment_gateway_report_aggregate import (
    PaymentGatewayReportAggregate,
)
from ths_common.constants.billing_constants import PaymentTypes
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.utils.collectionutils import chunks


class PaymentDataReportGenerator(BaseReportGenerator):
    def __init__(self, bill_ids):
        self.booking_repository = locate_instance(BookingRepository)
        self.bill_repository = locate_instance(BillRepository)
        self.invoice_repository = locate_instance(InvoiceRepository)
        self.hotel_repository = locate_instance(HotelRepository)
        self.payment_service_client = locate_instance(PaymentServiceClient)
        self.tenant_settings = locate_instance(TenantSettings)
        self.gateway_payment_modes = [
            'payment_service',
            'razorpay_api',
            'razorpay_payment_gateway',
            'phone_pe',
            'treebo_points',
        ]
        self.bill_ids = bill_ids

    def generate(self):
        pg_report_aggregates = []
        date = finance_context.posting_date
        payment_push_config = self.tenant_settings.get_setting_value(
            TenantSettingName.PAYMENT_DATA_PUSH_CONFIG.value,
        )

        if self.bill_ids:
            print("IF CASE")
            payments_charges_data = self.bill_repository.get_posted_payments_on_given_bills_for_given_payment_modes(
                self.bill_ids
            )
            cs_refunds_charges_data = (
                self.bill_repository.get_cs_refunds_posted_for_given_bills_or_date(
                    bill_ids=self.bill_ids
                )
            )
            bill_ids = list(
                {payment.bill_id for payment in payments_charges_data}
            ) + list({refund.bill_id for refund in cs_refunds_charges_data})
            bookings_data = self.booking_repository.get_booking_data_for_given_bills(
                bill_ids
            )
            filtered_payments_charges_data = payments_charges_data

        else:
            print("ELSE CASE")
            payments_to_push_on_booking_checkout_basis = (
                self._get_payments_to_push_on_booking_checkout_basis(
                    payment_push_config["checkout_pay_modes"]
                )
            )
            print("PAYMENTS TO PUSH ON BOOKING CHECKOUT BASIS:", payments_to_push_on_booking_checkout_basis)
            posted_payments = self.bill_repository.get_payments_posted_on_given_date(
                date
            )
            payments_to_push_after_night_audit = [
                payment
                for payment in posted_payments
                if payment.payment_mode in payment_push_config["paymentdate_pay_modes"]
            ]

            payments_charges_data = (
                posted_payments + payments_to_push_on_booking_checkout_basis
            )
            cs_refunds_charges_data = (
                self.bill_repository.get_cs_refunds_posted_for_given_bills_or_date(
                    posted_date=date
                )
            )

            bill_ids = list(
                {payment.bill_id for payment in payments_charges_data}
            ) + list({refund.bill_id for refund in cs_refunds_charges_data})
            bookings_data = self.booking_repository.get_booking_data_for_given_bills(
                bill_ids
            )
            payments_added_after_booking_checkout = self.payments_got_added_after_booking_gets_checked_out_or_marked_cancelled_no_show(
                posted_payments, bookings_data, payment_push_config
            )

            filtered_payments_charges_data = (
                payments_to_push_on_booking_checkout_basis
                + payments_to_push_after_night_audit
                + payments_added_after_booking_checkout
            )
        filtered_bill_ids = [
            payment.bill_id for payment in filtered_payments_charges_data
        ] + [cs_refund.bill_id for cs_refund in cs_refunds_charges_data]
        billed_entities_data = self.bill_repository.get_billed_entities_for_bills(
            filtered_bill_ids
        )
        invoices_data = self.invoice_repository.get_invoice_data_for_given_bills(
            filtered_bill_ids
        )
        hotel_ids = [booking.hotel_id for booking in bookings_data]
        hotels_data = self.hotel_repository.get_hotel_data_for_given_hotels(hotel_ids)
        payment_gateway_data = self._get_payment_gateway_data(
            filtered_payments_charges_data
        )

        hotel_id_to_name_mapping = {
            hotel_data.hotel_id: hotel_data.hotel_name for hotel_data in hotels_data
        }
        payment_ref_id_to_gateway_data_mapping = {
            pg_data.payment_id: pg_data for pg_data in payment_gateway_data
        }
        bill_id_to_booking_data_mapping = {
            booking.bill_id: booking for booking in bookings_data
        }
        bill_id_to_invoice_data_mapping = self._get_bill_id_to_invoice_data_mapping(
            invoices_data
        )
        bill_id_to_billed_entity_data_mapping = (
            self._get_bill_id_to_billed_entity_data_mapping(billed_entities_data)
        )

        for payment_charge_data in filtered_payments_charges_data:
            pg_data_for_payment = payment_ref_id_to_gateway_data_mapping.get(
                payment_charge_data.payment_ref_id, None
            )
            booking_data_for_payment = bill_id_to_booking_data_mapping.get(
                payment_charge_data.bill_id, None
            )
            invoices_data_for_payment = bill_id_to_invoice_data_mapping.get(
                payment_charge_data.bill_id, []
            )
            billed_entities_data_for_payment = (
                bill_id_to_billed_entity_data_mapping.get(
                    payment_charge_data.bill_id, []
                )
            )
            invoice_data_for_payment = self._get_required_invoice_data_for_payment(
                invoices_data_for_payment, payment_charge_data
            )
            billed_entity_data_for_payment = (
                self._get_required_billed_entity_data_for_payment(
                    billed_entities_data_for_payment, payment_charge_data
                )
            )
            hotel_name = hotel_id_to_name_mapping.get(
                booking_data_for_payment.hotel_id, None
            )
            pg_report_aggregates.append(
                PaymentGatewayReportAggregate(
                    payment_push_config,
                    payment_charge_data,
                    pg_data_for_payment,
                    booking_data_for_payment,
                    invoice_data_for_payment,
                    billed_entity_data_for_payment,
                    hotel_name,
                )
            )

        for cs_refund_charge_data in cs_refunds_charges_data:
            booking_data_for_payment = bill_id_to_booking_data_mapping.get(
                cs_refund_charge_data.bill_id, None
            )
            billed_entities_data_for_payment = (
                bill_id_to_billed_entity_data_mapping.get(
                    cs_refund_charge_data.bill_id, []
                )
            )
            billed_entity_data_for_payment = (
                self._get_required_billed_entity_data_for_payment(
                    billed_entities_data_for_payment, cs_refund_charge_data
                )
            )
            hotel_name = hotel_id_to_name_mapping.get(
                booking_data_for_payment.hotel_id, None
            )

            pg_report_aggregates.append(
                PaymentGatewayReportAggregate(
                    payment_push_config,
                    payment_charge_data=cs_refund_charge_data,
                    booking_data=booking_data_for_payment,
                    billed_entity_data=billed_entity_data_for_payment,
                    hotel_name=hotel_name,
                )
            )
        return pg_report_aggregates

    def _get_payment_gateway_data(self, payments_charges_data):
        payment_ref_ids = list(
            {
                payment.payment_ref_id
                for payment in payments_charges_data
                if payment.payment_mode in self.gateway_payment_modes
                and payment.payment_type == PaymentTypes.PAYMENT.value
            }
        )
        payment_gateway_data = []
        for payment_ref_ids in chunks(payment_ref_ids, 150):
            payment_gateway_data.extend(
                self.payment_service_client.fetch_payment_gateway_data(payment_ref_ids)
            )
        return [PaymentGatewayDataDto(pg_data) for pg_data in payment_gateway_data]

    @staticmethod
    def _get_bill_id_to_invoice_data_mapping(invoice_data):
        bill_id_to_invoice_data_mapping = {}
        for inv_data in invoice_data:
            if inv_data.bill_id not in bill_id_to_invoice_data_mapping:
                bill_id_to_invoice_data_mapping[inv_data.bill_id] = [inv_data]
            else:
                bill_id_to_invoice_data_mapping[inv_data.bill_id].append(inv_data)
        return bill_id_to_invoice_data_mapping

    @staticmethod
    def _get_bill_id_to_billed_entity_data_mapping(billed_entities_data):
        bill_id_to_billed_entity_data_mapping = {}
        for bill_entity_data in billed_entities_data:
            if bill_entity_data.bill_id not in bill_id_to_billed_entity_data_mapping:
                bill_id_to_billed_entity_data_mapping[bill_entity_data.bill_id] = [
                    bill_entity_data
                ]
            else:
                bill_id_to_billed_entity_data_mapping[bill_entity_data.bill_id].append(
                    bill_entity_data
                )
        return bill_id_to_billed_entity_data_mapping

    @staticmethod
    def _get_required_invoice_data_for_payment(
        invoices_data_for_payment, payment_charge_data
    ):
        if not payment_charge_data.billed_entity_account_number:
            return None
        invoice_data = None
        for inv_data in invoices_data_for_payment:
            if (
                inv_data.billed_entity_id
                and inv_data.billed_entity_account_number
                and (
                    (inv_data.billed_entity_id == payment_charge_data.billed_entity_id)
                    and (
                        inv_data.billed_entity_account_number
                        == payment_charge_data.billed_entity_account_number
                    )
                )
            ):
                invoice_data = inv_data
        return invoice_data

    @staticmethod
    def _get_required_billed_entity_data_for_payment(
        billed_entity_data_for_payment, payment_charge_data
    ):
        billed_entity_data = None
        for entity_data in billed_entity_data_for_payment:
            if (
                entity_data.billed_entity_id
                == payment_charge_data.payor_billed_entity_id
            ):
                billed_entity_data = entity_data
        return billed_entity_data

    def _get_payments_to_push_on_booking_checkout_basis(self, payment_modes):
        bookings_data = self._get_bookings()
        bill_ids = [booking_data.bill_id for booking_data in bookings_data]
        payments = self.bill_repository.get_posted_payments_on_given_bills_for_given_payment_modes(
            bill_ids, payment_modes
        )
        return payments

    def _get_bookings(self):
        date = finance_context.posting_date
        checked_out_bookings = (
            self.booking_repository.load_bookings_got_checked_out_on_given_date(date)
        )
        no_show_cancelled_bookings = (
            self.booking_repository.load_bookings_got_cancelled_or_noshow_on_given_date(
                date
            )
        )
        bookings = checked_out_bookings + no_show_cancelled_bookings
        bookings = [BookingDetailsDto(booking) for booking in bookings]
        return bookings

    @staticmethod
    def payments_got_added_after_booking_gets_checked_out_or_marked_cancelled_no_show(
        payments, bookings_data, payment_push_config
    ):
        bill_id_to_booking_data_mapping = {
            booking.bill_id: booking for booking in bookings_data
        }
        filtered_payments = []
        for payment in payments:
            booking = bill_id_to_booking_data_mapping.get(payment.bill_id)
            if payment.payment_mode in payment_push_config["checkout_pay_modes"]:
                booking_actual_checkout_date = (
                    booking.actual_checkout_calendar_date
                    or booking.actual_checkout_date
                )

                if booking.status == BookingStatus.CHECKED_OUT.value and (
                    payment.posted_date > booking_actual_checkout_date.date()
                ):
                    filtered_payments.append(payment)
                elif booking.status in (
                    BookingStatus.NOSHOW.value,
                    BookingStatus.CANCELLED.value,
                ) and (payment.posted_date > booking.cancellation_datetime.date()):
                    filtered_payments.append(payment)
        return filtered_payments
