from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.domain.catalog.repositories import (
    RoomTypeRepository,
    SkuCategoryRepository,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.in_touch_reports.code_file_report.code_report_generator import (
    InTouchCodeReportGenerator,
)
from prometheus.reporting.in_touch_reports.dtos.in_touch_code_dto import CodeFileDTO
from prometheus.reporting.utils import CsvWriter
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.reporting_constants import InTouchCodeReportCodeType


@register_instance(
    dependencies=[
        RoomTypeRepository,
        CatalogServiceClient,
        RateManagerClient,
        SkuCategoryRepository,
    ]
)
class InTouchCodeReportingService:
    def __init__(
        self,
        room_type_repository,
        catalog_service_client,
        rate_manager_client,
        sku_category_repository,
    ):
        self.room_type_repository = room_type_repository
        self.catalog_service_client = catalog_service_client
        self.rate_manager_client = rate_manager_client
        self.sku_category_repository = sku_category_repository

    def base_report_data(self, hotel_aggregate, report_date):
        _rate_plans = []
        rate_plans = self.rate_manager_client.get_rate_plans(hotel_aggregate.hotel_id)
        for rate_plan in rate_plans:
            _rate_plans.append(
                CodeFileDTO(
                    code=rate_plan.short_code,
                    code_description=rate_plan.name,
                    code_reference=rate_plan.rate_plan_id,
                    code_type=InTouchCodeReportCodeType.RATE_PLAN.value,
                )
            )

        _channel_codes = []
        channels = self.catalog_service_client.get_channels()
        for channel in channels:
            _channel_codes.append(
                CodeFileDTO(
                    code=channel.get('id'),
                    code_description=channel.get('description'),
                    code_reference=channel.get('reference_id'),
                    code_type=InTouchCodeReportCodeType.ORIGIN_CODE.value,
                )
            )

        _rooms = []
        room_type_map = self.room_type_repository.load_type_map()
        rooms = hotel_aggregate.room_type_configs
        for room in rooms:
            _rooms.append(
                CodeFileDTO(
                    code=room.room_type_id,
                    code_description=None,
                    code_reference=room_type_map[room.room_type_id].room_type.type,
                    code_type=InTouchCodeReportCodeType.ROOM.value,
                )
            )

        _countries = []
        countries = self.catalog_service_client.get_countries()
        for country in countries:
            _countries.append(
                CodeFileDTO(
                    code=country.get('iso_code'),
                    code_description=country.get('description'),
                    code_reference=country.get('reference_id'),
                    code_type=InTouchCodeReportCodeType.COUNTRY.value,
                )
            )

        _states = []
        for country in countries:
            states = self.catalog_service_client.get_states(
                country_id=country.get('id')
            )
            for state in states:
                _states.append(
                    CodeFileDTO(
                        code=state.get('code'),
                        code_description=state.get('name'),
                        code_reference=state.get('id'),
                        code_type=InTouchCodeReportCodeType.STATES.value,
                    )
                )

        _sub_channels = []
        for channel in channels:
            sub_channels = self.catalog_service_client.get_sub_channel_for_channel(
                channel_code=channel.get('id')
            )
            for sub_channel in sub_channels:
                _sub_channels.append(
                    CodeFileDTO(
                        code=sub_channel.get('channel_id'),
                        code_description=sub_channel.get('name'),
                        code_reference=sub_channel.get('id'),
                        code_type=InTouchCodeReportCodeType.MARKET_CODE.value,
                    )
                )

        currency_code = [
            CodeFileDTO(
                code=hotel_aggregate.base_currency.value,
                code_description=hotel_aggregate.base_currency.name,
                code_reference=None,
                code_type=InTouchCodeReportCodeType.CURRENCY.value,
            )
        ]

        reservation_status_code = [
            CodeFileDTO(
                code=booking_status,
                code_description=None,
                code_reference=None,
                code_type=InTouchCodeReportCodeType.BOOKING_STATUS.value,
            )
            for booking_status in BookingStatus.valid_booking_status()
        ]

        _sku_codes = []

        skus = self.catalog_service_client.get_skus(hotel_aggregate.hotel_id)
        sku_category_map = {
            sku_category_aggregate.sku_category.sku_category_id: sku_category_aggregate.sku_category
            for sku_category_aggregate in self.sku_category_repository.load_all()
        }

        if skus:
            for sku in skus:
                hsn_code = (
                    sku_category_map[sku.sku_category_id].item_code.value
                    if sku.sku_category_id in sku_category_map
                    else ""
                )
                _sku_codes.append(
                    CodeFileDTO(
                        code=sku.expense_item_id,
                        code_description=sku.name,
                        code_reference=hsn_code,
                        code_type=InTouchCodeReportCodeType.SKU_CODE.value,
                    )
                )

        closure_date = [
            CodeFileDTO(
                code=report_date,
                code_description=None,
                code_reference=None,
                code_type=InTouchCodeReportCodeType.CLOSURE_DATE.value,
            )
        ]

        return (
            _rate_plans
            + _channel_codes
            + _countries
            + _rooms
            + _sub_channels
            + reservation_status_code
            + currency_code
            + _sku_codes
            + closure_date
        )

    @staticmethod
    def _generate_csv_report(report_data, csv_writer):
        report_aggregates = [
            InTouchCodeReportGenerator(data).generate() for data in report_data
        ]
        csv_writer.write_aggregates(
            report_aggregates, InTouchCodeReportGenerator.REPORT_COLUMNS
        )

    def generate_csv_report(self, report_date, hotel_aggregate=None):
        report_data = self.base_report_data(hotel_aggregate, report_date)
        file_path = InTouchCodeReportGenerator.generate_in_touch_code_report_file_name(
            report_date
        )
        folder_path = f"{get_current_tenant_id()}/in-touch-data-files/{hotel_aggregate.hotel_id}/{report_date}/"

        with CsvWriter(file_path) as csv_writer:
            self._generate_csv_report(report_data, csv_writer)
            AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                folder_path,
                csv_writer.file_path,
                InTouchCodeReportGenerator.get_default_expiration_time(),
            )

        return report_data
