from prometheus.integration_tests.builders.common_request_builder import bill_repo
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.builders.common_request_builder import Payment


class BulkEditPayment(object):
    def __init__(self, sheet_name, test_case_id, bill_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = []
        for data in test_data:
            self.data.append(Payment(data).__dict__)
        self.resource_version = bill_repo().load(bill_id).bill.version
