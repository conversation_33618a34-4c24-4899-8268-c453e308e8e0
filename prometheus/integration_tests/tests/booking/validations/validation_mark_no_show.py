import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationMarkNoShow(BaseValidations):
    def __init__(self, client_, test_case_id, response, booking_request, booking_id):
        self.test_data = get_test_case_data(sheet_names.mark_no_show_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.booking_request = booking_request
        self.booking_id = booking_id

    def validate_response(self):
        assert_(self.response['data']['action_type'], self.test_data['expected_action_type'])
        assert_(self.response['data']['reversal'], self.test_data['expected_reversal_action'])
        assert_(self.response['data']['status'], self.test_data['expected_status'])

        booking_action_response = self.booking_request.get_allowed_booking_actions(self.client, self.booking_id, 200)
        self.validate_booking_allowed_actions(booking_action_response,
                                              self.test_data['expected_allowed_booking_actions'],
                                              self.test_data['expected_allowed_roomstay_actions'])

        booking_response = self.booking_request.get_booking_request(self.client, self.booking_id, 200)
        for room_stay in booking_response['data']['room_stays']:
            assert len(room_stay['date_wise_charge_ids']) > 0
        room_stay_ids = sanitize_test_data(self.test_data['room_stay_ids']).split(',') if sanitize_test_data(
            self.test_data['room_stay_ids']) else []
        guest_stay_ids_for_room = sanitize_test_data(self.test_data['guest_stay_ids'])
        if guest_stay_ids_for_room:
            for room_stay_id, guest_stay_ids in zip(room_stay_ids, guest_stay_ids_for_room.split('#')):
                for booking_response_room_stays in booking_response['data']['room_stays']:
                    if int(room_stay_id) == booking_response_room_stays['room_stay_id']:
                        for guest_stay_id in guest_stay_ids.split(','):
                            for booking_response_guest_stays in booking_response_room_stays['guest_stays']:
                                if int(guest_stay_id) == booking_response_guest_stays['guest_stay_id']:
                                    assert_(booking_response_guest_stays['status'], 'noshow')
        elif room_stay_ids:
            for room_stay_id in room_stay_ids:
                for booking_response_room_stays in booking_response['data']['room_stays']:
                    if int(room_stay_id) == booking_response_room_stays['room_stay_id']:
                        assert_(booking_response_room_stays['status'], 'noshow')
        else:
            assert_(booking_response['data']['status'], 'noshow')

    def validate_billed_entity_response(self, billing_request, bill_id):
        actual_billed_entity_response = billing_request.get_bill_entity_request(self.client, bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response,
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)

    def validate_charge_and_expense_status(self, expense_request, billing_request, bill_id):
        expenses_response = expense_request.get_expense_detail(self.client, 200, self.booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = billing_request.get_charge_request(self.client, bill_id, 200,
                                                                   expense['charge_id'])['data']
            assert_(expense['status'], get_charge_detail['status'])

    def validate_commissions(self):
        expected_data = self.test_data
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_charges(self, billing_request):
        expected_data = self.test_data['expected_charge_details']
        if expected_data:
            actual_charge_details = billing_request.get_bill_charges(self.client,
                                                                          self.booking_request.bill_id, 200)['data']
            expected_charge_details = json.loads(expected_data)
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
                expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
                self.validate_charge(actual_charge_detail, expected_charge_detail)
