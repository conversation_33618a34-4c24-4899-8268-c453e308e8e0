import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, return_date, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationEditRatePlanCharge(BaseValidations):

    def __init__(self, client, test_case_id, response, hotel_id, bill_id, booking_request, billing_request):
        self.test_data = get_test_case_data(sheet_names.charges_v2_sheet_name, test_case_id)[0]
        self.client = client
        self.test_case_id = test_case_id
        self.response = response
        self.hotel_id = hotel_id
        self.bill_id = bill_id
        self.booking_request = booking_request
        self.billing_request = billing_request

    def validate_response(self):
        expected_room_stay_details = self.booking_request.get_room_stay_details_v2(self.client, self.test_case_id, 200,
                                                                                   sheet_names.charges_v2_sheet_name)
        actual_room_data = self.response['data']
        expected_room_data = expected_room_stay_details['data']
        assert_(actual_room_data['actual_checkin_date'], expected_room_data['actual_checkin_date'])
        assert_(actual_room_data['actual_checkout_date'], expected_room_data['actual_checkout_date'])
        assert_(actual_room_data['allowed_actions'].sort(), expected_room_data['allowed_actions'].sort())
        assert_(actual_room_data['checkin_date'], expected_room_data['checkin_date'])
        assert_(actual_room_data['checkout_date'], expected_room_data['checkout_date'])
        assert_(actual_room_data['disallow_charge_addition'], expected_room_data['disallow_charge_addition'])
        assert_(actual_room_data['is_overflow'], expected_room_data['is_overflow'])
        assert_(actual_room_data['room_stay_id'], expected_room_data['room_stay_id'])
        assert_(actual_room_data['room_type_id'], expected_room_data['room_type_id'])
        assert_(actual_room_data['status'], expected_room_data['status'])
        assert_(actual_room_data['stay_end'], expected_room_data['stay_end'])
        assert_(actual_room_data['stay_start'], expected_room_data['stay_start'])

        actual_rate_plan_data_sorted = sorted(actual_room_data['room_rate_plans'], key=lambda i: int(i['rate_plan_id']))
        expected_rate_plan_data_sorted = sorted(expected_room_data['room_rate_plans_ids'])

        for actual_rate_plan, expected_rate_plan_id in zip(actual_rate_plan_data_sorted,
                                                           expected_rate_plan_data_sorted):
            assert_(actual_rate_plan['rate_plan_id'], expected_rate_plan_id)

        for actual_date_wise_charge, expected_date_wise_charge in zip(actual_room_data['date_wise_charge_ids'],
                                                                      expected_room_data['date_wise_charge_ids']):
            assert_(actual_date_wise_charge['charge_id'], expected_date_wise_charge['charge_id'])
            assert_(actual_date_wise_charge['charge_date'], expected_date_wise_charge['charge_date'])

        for actual_guest_data, expected_guest_data in zip(actual_room_data['guest_stays'],
                                                          expected_room_data['guest_stays']):
            assert_(actual_guest_data['age_group'], expected_guest_data['age_group'])
            assert_(actual_guest_data['actual_checkin_date'], expected_guest_data['actual_checkin_date'])
            assert_(actual_guest_data['checkin_date'], expected_guest_data['checkin_date'])
            assert_(actual_guest_data['stay_start'], expected_guest_data['stay_start'])
            assert_(actual_guest_data['checkout_date'], expected_guest_data['checkout_date'])
            assert_(actual_guest_data['actual_checkout_date'], expected_guest_data['actual_checkout_date'])
            assert_(actual_guest_data['stay_end'], expected_guest_data['stay_end'])
            assert_(actual_guest_data['allowed_actions'].sort(), expected_guest_data['allowed_actions'].sort())
            if sanitize_test_data(actual_guest_data['guest_allocation']):
                assert_(actual_guest_data['guest_allocation']['guest_id'], expected_guest_data['guest_id'])
            assert_(actual_guest_data['guest_stay_id'], expected_guest_data['guest_stay_id'])
            assert_(actual_guest_data['status'], expected_guest_data['status'])

    def validate_bill_summary_and_charges(self):
        get_bill_response = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200)['data']
        actual_bill_summary = get_bill_response['summary']
        expected_bill_summary = json.loads(self.test_data['expected_billed_summary_response'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)
        actual_charge_details = sorted(get_bill_response['charges'], key=lambda i: i['charge_id'])
        expected_charge_details = sorted(json.loads(self.test_data['expected_charge_details']),
                                         key=lambda i: i['charge_id'])
        for actual_charge_detail, expected_charge_detail in zip(actual_charge_details, expected_charge_details):
            actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
            expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
            self.validate_charge(actual_charge_detail, expected_charge_detail, self.hotel_id)

    def validate_billed_entity_response(self):
        actual_billed_entity_response = self.billing_request.get_bill_entity_request(self.client, self.bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response,
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted,
                                    self.hotel_id)

    def validate_commissions(self):
        if sanitize_test_data(self.test_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(self.test_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_tax_with_charges(self):
        actual_charge_details = self.billing_request.get_bill_charges(self.client, self.bill_id, 200)['data']
        expected_charge_details = json.loads(self.test_data['expected_charge_details'])
        sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
        sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
        for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                sorted_expected_charge_details):
            actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
            expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
            self.validate_charge(actual_charge_detail, expected_charge_detail)
