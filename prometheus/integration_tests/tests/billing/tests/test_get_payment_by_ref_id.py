import pytest

from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.billing.validations.validation_get_payment_by_ref_id import (
    ValidationGetPaymentByRefId
)


class TestGetPaymentByRefId(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message,"
        " dev_message, error_payload, skip_case, skip_message, post_payment", [
            ("GetPaymentByRefId_01", 'Get payment having done status', ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "",
             "", False, "", False),
            ("GetPaymentByRefId_02", 'Get multiple payment having done status with same ref id',
             ADD_CONFIRMED_PAYMENT_38, 200, None, "", "", "", "", False, "", False),
            ("GetPaymentByRefId_03", 'Get multiple payment having done status with same ref id on diff booking',
             ADD_CONFIRMED_PAYMENT_39, 200, None, "", "", "", "", False, "", False),
            ("GetPaymentByRefId_04", 'Get payment having posted status', ADD_CONFIRMED_PAYMENT_17, 200, None, "", "",
             "", "", False, "", True),
            ("GetPaymentByRefId_05", 'Get multiple payment having done and posted status', ADD_CONFIRMED_PAYMENT_39,
             200, None, "", "", "", "", False, "", True),
            ("GetPaymentByRefId_06", 'Get payment having cancelled status', EDIT_PAYMENT_01, 200, None, "", "", "", "",
             False, "", False),
            ("GetPaymentByRefId_07", 'Get multiple payment having done and cancelled status', EDIT_PAYMENT_02, 200,
             None, "", "", "", "", False, "", False),
            ("GetPaymentByRefId_08", 'Get payment having posted status, which is invoiced', FULL_CHECKOUT_01_V2, 200,
             None, "", "", "", "", False, "", False),
            ("GetPaymentByRefId_09", 'Get payment where booking contains different ref id', ADD_CONFIRMED_PAYMENT_40,
             200, None, "", "", "", "", False, "", False),
            ("GetPaymentByRefId_10", 'Get payment and refund with same ref id', ADD_CONFIRMED_PAYMENT_22, 200, None, "",
             "", "", "", False, "", False),
            ("GetPaymentByRefId_11", 'Provide payment id as None', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             False, "", False),
        ])
    @pytest.mark.regression
    def test_get_payment_by_ref_id(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message, post_payment):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        self.billing_request.payment_ref_id = None

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if post_payment:
            query_execute(db_queries.UPDATE_PAYMENT_TO_POSTED.format(bill_id=self.booking_request.bill_id))

        response = self.billing_request.get_payment_br_ref_id_request(client_, status_code,
                                                                      self.billing_request.payment_ref_id, user_type)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id):
        validation = ValidationGetPaymentByRefId(response, test_case_id)
        validation.validate_response()
