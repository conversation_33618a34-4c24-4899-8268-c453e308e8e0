import datetime
import threading

import phonenumbers
from treebo_commons.money.constants import CurrencyType
from treebo_commons.request_tracing import context
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.config.treebo_config import TreeboConfig
from ths_common.constants.hotel_constants import BrandCodes, ManagedBy
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import MissingCurrentBusinessDate, ValidationException
from ths_common.value_objects import NotAssigned, PhoneNumber, VendorDetails


class HotelDtoForContext:
    def __init__(
        self,
        hotel_id,
        switch_over_time,
        free_late_checkout_time,
        checkin_time,
        checkout_time,
        base_currency,
        current_business_date,
        timezone=None,
    ):
        self.hotel_id = hotel_id
        self.switch_over_time = switch_over_time
        self.free_late_checkout_time = free_late_checkout_time
        self.checkin_time = checkin_time
        self.checkout_time = checkout_time
        self.base_currency = base_currency
        self.current_business_date = current_business_date
        self.timezone = timezone

    def hotel_checkin_date(self, checkin_datetime):
        checkin_date = dateutils.to_date(checkin_datetime)
        checkin_time = dateutils.to_time(checkin_datetime)
        if checkin_time < self.switch_over_time:
            return dateutils.subtract(checkin_date, days=1)
        else:
            return checkin_date

    def hotel_checkout_date(self, checkout_datetime):
        checkout_date = dateutils.to_date(checkout_datetime)
        checkout_time = dateutils.to_time(checkout_datetime)
        if checkout_time > self.free_late_checkout_time:
            return dateutils.add(checkout_date, days=1)
        else:
            return checkout_date


class HotelContext(object):
    hotel_defaults = {
        "switch_over_time": "06:00:00",
        "free_late_checkout_time": "15:00:00",
        "checkin_grace_time": 540,  # minutes
        "checkout_grace_time": 360,  # minutes
        "system_freeze_time": "15:00:00",
    }

    def __init__(
        self,
        switch_over_time,
        checkin_time,
        checkout_time,
        free_late_checkout_time,
        hotel_id,
        base_currency,
        current_business_date,
        checkin_grace_time=None,
        checkout_grace_time=None,
        state=None,
        legal_signature=None,
        legal_state=None,
        housekeeping_enabled=None,
        managed_by=None,
        live_date=None,
        state_code=None,
        name=None,
        gst_details=None,
        address=None,
        brands=None,
        is_active=False,
        has_lut=False,
        phone_number=None,
        country_code=None,
        email=None,
        system_freeze_time=None,
        is_test=False,
    ):
        if switch_over_time:
            self.switch_over_time = (
                datetime.datetime.strptime(switch_over_time, "%H:%M:%S").time()
                if isinstance(switch_over_time, str)
                else switch_over_time
            )
        else:
            self.switch_over_time = datetime.datetime.strptime(
                HotelContext.hotel_defaults['switch_over_time'], "%H:%M:%S"
            ).time()

        if checkin_time:
            self.checkin_time = (
                datetime.datetime.strptime(checkin_time, "%H:%M:%S").time()
                if isinstance(checkin_time, str)
                else checkin_time
            )
        else:
            self.checkin_time = None

        if checkout_time:
            self.checkout_time = (
                datetime.datetime.strptime(checkout_time, "%H:%M:%S").time()
                if isinstance(checkout_time, str)
                else checkout_time
            )
        else:
            self.checkout_time = None

        if free_late_checkout_time:
            self.free_late_checkout_time = (
                datetime.datetime.strptime(free_late_checkout_time, "%H:%M:%S").time()
                if isinstance(free_late_checkout_time, str)
                else free_late_checkout_time
            )
        else:
            self.free_late_checkout_time = datetime.datetime.strptime(
                HotelContext.hotel_defaults['free_late_checkout_time'], "%H:%M:%S"
            ).time()

        if checkin_grace_time:
            self.checkin_grace_time = datetime.timedelta(minutes=checkin_grace_time)
        else:
            self.checkin_grace_time = datetime.timedelta(
                minutes=HotelContext.hotel_defaults['checkin_grace_time']
            )

        if checkout_grace_time:
            self.checkout_grace_time = datetime.timedelta(minutes=checkout_grace_time)
        else:
            self.checkout_grace_time = datetime.timedelta(
                minutes=HotelContext.hotel_defaults['checkout_grace_time']
            )

        if system_freeze_time:
            self.system_freeze_time = (
                datetime.datetime.strptime(system_freeze_time, "%H:%M:%S").time()
                if isinstance(system_freeze_time, str)
                else system_freeze_time
            )
        else:
            self.system_freeze_time = datetime.datetime.strptime(
                HotelContext.hotel_defaults['system_freeze_time'], "%H:%M:%S"
            ).time()

        self.live_date = live_date
        self.managed_by = managed_by
        self.hotel_id = hotel_id
        self.hotel_state_code = state_code
        self.state = state
        self.legal_state = legal_state
        self.hotel_name = name
        self.hotel_gst_details = gst_details
        self.hotel_address = address
        self.housekeeping_enabled = housekeeping_enabled
        self.legal_signature = legal_signature
        self.is_active = is_active
        self.brands = brands
        self.base_currency = base_currency
        self.has_lut = has_lut if has_lut is not None else False
        self.phone_number = phone_number
        self.country_code = country_code if country_code else "IN"
        self.email = email
        self._current_business_date = current_business_date
        self.hotel_uses_post_tax_price = None
        self.is_test = is_test

    @property
    def legal_state_id(self):
        return self.legal_state.id if self.legal_state else self.state.id

    @property
    def brand_code(self):
        if self.brands:
            return list(self.brands)[0]
        return None

    @property
    def state_id(self):
        return self.state.id

    @staticmethod
    def create_new(hotel_aggregate):
        hotel = hotel_aggregate.hotel

        hotel_context = HotelContext(
            switch_over_time=hotel.switch_over_time,
            checkin_time=hotel.checkin_time,
            checkout_time=hotel.checkout_time,
            free_late_checkout_time=hotel.free_late_checkout_time,
            managed_by=None,
            live_date=hotel.launched_date,
            hotel_id=hotel.hotel_id,
            state_code=hotel.state_code,
            name=hotel.name,
            gst_details=hotel.gst_details,
            address=hotel.address,
            checkin_grace_time=hotel.checkin_grace_time,
            checkout_grace_time=hotel.checkout_grace_time,
            state=hotel.state,
            legal_signature=hotel.legal_signature,
            legal_state=hotel.legal_state,
            housekeeping_enabled=hotel.housekeeping_enabled,
            brands=hotel.brands,
            is_active=hotel.is_active,
            has_lut=hotel.has_lut,
            base_currency=hotel_aggregate.hotel.base_currency,
            phone_number=None,
            country_code=None,
            email=None,
            current_business_date=hotel.current_business_date,
            system_freeze_time=hotel.system_freeze_time,
            is_test=hotel.is_test,
        )
        return hotel_context

    def hotel_checkin_date(self, checkin_datetime):
        checkin_date = dateutils.to_date(checkin_datetime)
        checkin_time = dateutils.to_time(checkin_datetime)
        if checkin_time < self.switch_over_time:
            return dateutils.subtract(checkin_date, days=1)
        else:
            return checkin_date

    def hotel_checkout_date(self, checkout_datetime):
        checkout_date = dateutils.to_date(checkout_datetime)
        checkout_time = dateutils.to_time(checkout_datetime)
        if checkout_time > self.free_late_checkout_time:
            return dateutils.add(checkout_date, days=1)
        else:
            return checkout_date

    def is_managed_by_crs(self):
        return self.managed_by == ManagedBy.CRS

    def is_managed_by_hx(self):
        return self.managed_by == ManagedBy.HX

    def is_migration_in_progress(self):
        return self.managed_by == ManagedBy.MIGRATION_IN_PROGRESS

    def current_date(self):
        return self._current_business_date

    def next_switch_over_date_time(self):
        return dateutils.datetime_at_given_time(
            self.current_date(), self.switch_over_time
        )

    def attach_switch_over_time_to_date(self, date_: datetime.date):
        return dateutils.datetime_at_given_time(date_, self.switch_over_time)

    def attach_free_late_checkout_time_to_date(self, date_: datetime.date):
        return dateutils.datetime_at_given_time(date_, self.free_late_checkout_time)

    def get_next_switch_over_date_time_after_time(self, date_time):
        date_ = dateutils.to_date(date_time)
        time_ = dateutils.to_time(date_time)
        if time_ < self.switch_over_time:
            switch_over_date = date_
        else:
            switch_over_date = dateutils.add(date_, days=1)
        return dateutils.datetime_at_given_time(switch_over_date, self.switch_over_time)

    def get_next_late_checkout_date_time_after_time(self, date_time):
        date_ = dateutils.to_date(date_time)
        time_ = dateutils.to_time(date_time)
        if time_ < self.free_late_checkout_time:
            switch_over_date = date_
        else:
            switch_over_date = dateutils.add(date_, days=1)
        return dateutils.datetime_at_given_time(
            switch_over_date, self.free_late_checkout_time
        )

    def next_late_checkout_date_time(self):
        return dateutils.datetime_at_given_time(
            self.current_date(), self.free_late_checkout_time
        )

    def validate_hotel_managed_by_crs(self, user_type):
        if not self.managed_by:
            raise ValidationException(ApplicationErrors.HOTEL_NOT_MANAGED_BY_CRS)
        if self.is_managed_by_hx():
            raise ValidationException(ApplicationErrors.HOTEL_NOT_MANAGED_BY_CRS)
        if (
            self.is_migration_in_progress()
            and user_type != UserType.CRS_MIGRATION_USER.value
        ):
            raise ValidationException(ApplicationErrors.HOTEL_NOT_MANAGED_BY_CRS)

    def build_vendor_details(self):
        if self.is_independent_hotel() or not crs_context.is_treebo_tenant():
            phonenumber = (
                phonenumbers.parse(self.phone_number, self.country_code)
                if self.phone_number
                else None
            )
            contact_details = dict(
                phone=PhoneNumber(
                    phonenumber.national_number, '+' + str(phonenumber.country_code)
                )
                if phonenumber
                else None,
                email=self.email,
                url=None,
            )
        else:
            contact_details = dict(
                phone=TreeboConfig.TREEBO_PHONE_NUMBER,
                email=TreeboConfig.TREEBO_EMAIL_ID,
                url=TreeboConfig.TREEBO_SUPPORT_URL,
            )
        vendor_info = VendorDetails(
            hotel_id=self.hotel_id,
            state_code=self.hotel_state_code,
            name=self.hotel_name,
            gst_details=self.hotel_gst_details,
            address=self.hotel_address,
            legal_signature=self.legal_signature,
            is_test=self.is_test,
            **contact_details
        )
        return vendor_info

    def hotel_date(self, request_datetime=None):
        """
        gives the current active date based on the switchover time.
        Args:
            request_datetime: the request date time based on which the date has to be figured out.
            if null current datetime will be taken.

        Returns: Date

        """
        if not request_datetime:
            request_datetime = dateutils.current_datetime()

        switchover_time = self.switch_over_time
        request_date = dateutils.to_date(request_datetime)

        if dateutils.to_time(request_datetime) < switchover_time:
            hotel_date = request_date - datetime.timedelta(days=1)
        else:
            hotel_date = request_date

        return hotel_date

    def is_independent_hotel(self):
        if not self.brands:
            return False
        for brand_code in self.brands:
            if brand_code == BrandCodes.INDEPENDENT:
                return True
        return False

    def is_hotel_uses_posttax_price(self):
        # TODO: refactor to remove this from hotel context
        if self.hotel_uses_post_tax_price is not None:
            return self.hotel_uses_post_tax_price
        hotel_uses_post_tax_price = True
        if self.hotel_id:
            from prometheus.application.hotel_settings.tenant_settings import (
                TenantSettings,
            )

            tenant_settings = locate_instance(TenantSettings)
            hotel_uses_post_tax_price = tenant_settings.hotel_uses_posttax_price(
                self.hotel_id
            )
        self.hotel_uses_post_tax_price = hotel_uses_post_tax_price
        return self.hotel_uses_post_tax_price

    def is_sms_or_whatsapp_enabled(self):
        """
        Check if SMS or WhatsApp communication is enabled based on the brand code.
        """
        return self.brand_code not in [BrandCodes.HOTELSUPERHERO]


class SellerContext(object):
    def __init__(self, base_currency, current_business_date, vendor_details=None):
        self.base_currency = base_currency
        self._current_business_date = current_business_date
        self.vendor_details = vendor_details

    def current_date(self):
        return self._current_business_date


class CrsContext(threading.local):
    def __init__(self):
        super(CrsContext, self).__init__()
        self.hotel_context = None
        self.hotel_wise_context = None
        self.current_booking_id = None
        self.current_booking_aggregate = None
        self.current_bill_aggregate = None
        self.action_id = None
        self.tenant_id = None
        self.user_data = None
        self.source_application = None
        self.seller_context = None
        self.aws_clients = dict()
        self.bypass_privilege_check = False
        self.bypass_access_entity_check = False
        self.current_business_date = None
        self.privileges_as_dict = NotAssigned
        self.role_privilege_dtos = NotAssigned
        self.room_type_map = None
        self._sftp_secret = None
        self.report_date = None

    def add_bypass_privilege_checks(self):
        self.bypass_privilege_check = True

    def should_bypass_privilege_checks(self):
        return self.bypass_privilege_check

    def add_bypass_access_entity_checks(self):
        self.bypass_access_entity_check = True

    def should_bypass_access_entity_checks(self):
        return self.bypass_access_entity_check

    def set_tenant_id(self, tenant_id):
        self.tenant_id = tenant_id

    def set_source_application(self, source_application):
        self.source_application = source_application

    def set_basic_hotel_context(self, hotel_dto_for_context: HotelDtoForContext):
        self.hotel_context = HotelContext(
            hotel_dto_for_context.switch_over_time,
            hotel_dto_for_context.checkin_time,
            hotel_dto_for_context.checkout_time,
            hotel_dto_for_context.free_late_checkout_time,
            hotel_dto_for_context.hotel_id,
            hotel_dto_for_context.base_currency,
            hotel_dto_for_context.current_business_date,
        )

        if not self.hotel_context.current_date():
            raise MissingCurrentBusinessDate(
                extra_payload=dict(hotel_id=hotel_dto_for_context.hotel_id)
            )
        timezone = (
            hotel_dto_for_context.timezone
            if hotel_dto_for_context.timezone
            else 'Asia/Kolkata'
        )
        context.set_app_timezone(timezone)
        return self.hotel_context

    def set_hotel_context(
        self, hotel_aggregate, hotel_config_aggregate=None
    ) -> HotelContext:
        hotel = hotel_aggregate.hotel
        timezone = hotel.timezone if hotel.timezone else 'Asia/Kolkata'
        context.set_app_timezone(timezone)
        managed_by = None
        base_currency = CurrencyType.INR
        if hotel_aggregate.hotel.base_currency:
            base_currency = hotel_aggregate.hotel.base_currency
        if hotel_config_aggregate:
            managed_by = hotel_config_aggregate.hotel_config.managed_by
        self.hotel_context = HotelContext(
            switch_over_time=hotel.switch_over_time,
            checkin_time=hotel.checkin_time,
            checkout_time=hotel.checkout_time,
            base_currency=base_currency,
            free_late_checkout_time=hotel.free_late_checkout_time,
            managed_by=managed_by,
            live_date=hotel.launched_date,
            hotel_id=hotel.hotel_id,
            state_code=hotel.state_code,
            name=hotel.name,
            gst_details=hotel.gst_details,
            address=hotel.address,
            checkin_grace_time=hotel.checkin_grace_time,
            checkout_grace_time=hotel.checkout_grace_time,
            state=hotel.state,
            legal_signature=hotel.legal_signature,
            legal_state=hotel.legal_state,
            housekeeping_enabled=hotel.housekeeping_enabled,
            brands=hotel.brands,
            is_active=hotel.is_active,
            has_lut=hotel.has_lut,
            phone_number=hotel.phone_number,
            country_code=hotel.country_code,
            email=hotel.email,
            current_business_date=hotel.current_business_date,
            system_freeze_time=hotel.system_freeze_time,
            is_test=hotel.is_test,
        )
        self.current_business_date = hotel.current_business_date
        if not self.current_business_date:
            raise MissingCurrentBusinessDate(
                extra_payload=dict(hotel_id=hotel.hotel_id)
            )
        return self.hotel_context

    def set_seller_context(self, seller_aggregate, vendor_details=None):
        self.seller_context = SellerContext(
            base_currency=seller_aggregate.seller.base_currency,
            current_business_date=seller_aggregate.seller.current_business_date,
            vendor_details=vendor_details,
        )
        self.current_business_date = seller_aggregate.seller.current_business_date
        return self.seller_context

    def get_seller_context(self) -> SellerContext:
        return self.seller_context

    def set_current_booking(self, booking_aggregate):
        self.current_booking_id = booking_aggregate.booking.booking_id
        self.current_booking_aggregate = booking_aggregate

    def set_current_bill(self, bill_aggregate):
        self.current_bill_aggregate = bill_aggregate

    def set_current_action_id(self, booking_action_aggregate):
        self.action_id = booking_action_aggregate.booking_action.action_id

    def get_hotel_context(self) -> HotelContext:
        return self.hotel_context

    def get_current_booking_id(self):
        return self.current_booking_id

    def get_current_booking(self):
        return self.current_booking_aggregate

    def get_current_bill(self):
        return self.current_bill_aggregate

    def get_current_action_id(self):
        return self.action_id

    def activate_hotel_context(self, hotel_id):
        self.hotel_context = self.hotel_wise_context.get(hotel_id)

    def set_user_data(self, user_data):
        self.user_data = user_data

    def get_current_hotel_id(self):
        if not self.user_data:
            return None
        return self.user_data.hotel_id

    def get_current_seller_id(self):
        if not self.user_data:
            return None
        return self.user_data.seller_id

    def is_treebo_tenant(self):
        """
        This function considers ten100 tenant as treebo tenant
        """
        treebo_tenant_ids = {
            TenantSettingName.TREEBO_TENANT_ID.value,
            TenantSettingName.TEN100_TENANT_ID.value,
        }
        if self.tenant_id:
            return self.tenant_id in treebo_tenant_ids
        elif get_current_tenant_id():
            return get_current_tenant_id() in treebo_tenant_ids
        return True

    def is_ten100_tenant(self):
        if self.tenant_id:
            return self.tenant_id == TenantSettingName.TEN100_TENANT_ID.value
        elif get_current_tenant_id():
            return get_current_tenant_id() == TenantSettingName.TEN100_TENANT_ID.value
        return False

    def is_pis_tenant(self):
        if self.tenant_id:
            return self.tenant_id == TenantSettingName.PIS_TENANT_ID.value
        elif get_current_tenant_id():
            return get_current_tenant_id() == TenantSettingName.PIS_TENANT_ID.value
        return False

    def set_room_type_map(self, room_type_map):
        self.room_type_map = room_type_map

    def get_room_type_map(self):
        return self.room_type_map

    def clear(self):
        self.hotel_context = None
        self.hotel_wise_context = None
        self.current_booking_id = None
        self.current_booking_aggregate = None
        self.current_bill_aggregate = None
        self.action_id = None
        self.tenant_id = None
        self.user_data = None
        self.aws_clients = dict()
        self.bypass_privilege_check = False
        self.bypass_access_entity_check = False
        self.current_business_date = None
        self.privileges_as_dict = NotAssigned
        self.role_privilege_dtos = NotAssigned
        self.room_type_map = None
        self.report_date = None

    def set_sftp_secret(self, sftp_secret):
        self._sftp_secret = sftp_secret

    def get_sftp_secret(self):
        return self._sftp_secret

    def set_report_date(self, report_date):
        self.report_date = report_date

    def get_report_date(self):
        return self.report_date


# When used for consumers, clear this after every message processing
crs_context = CrsContext()

# To store context of consumer, that should persist across all consumed messages. Like TenantId
consumer_context = CrsContext()
